/**
 * Auto-Discovery GoodyHut Collector - Modern TypeScript approach
 * Uses frida-il2cpp-bridge for automatic method discovery instead of manual RVA addresses
 * 
 * Benefits:
 * - Game version independent
 * - Automatic method discovery
 * - Type-safe TypeScript
 * - Robust error handling
 * - No manual RVA address maintenance
 */

// Import Il2Cpp bridge - using require for compatibility
const Il2Cpp = require('frida-il2cpp-bridge');

interface GoodyHutMethods {
    canCollect?: Il2Cpp.Method;
    finishCollect?: Il2Cpp.Method;
    config?: Il2Cpp.Method;
    sellRuins?: Il2Cpp.Method;
    getRewardType?: Il2Cpp.Method;
    getRewardAmount?: Il2Cpp.Method;
    update?: Il2Cpp.Method;
}

interface GoodyHutConfigMethods {
    getCleanUp?: Il2Cpp.Method;
    setCleanUp?: Il2Cpp.Method;
}

class AutoDiscoveryGoodyHutCollector {
    private assemblyImage?: Il2Cpp.Image;
    private goodyHutHelperClass?: Il2Cpp.Class;
    private goodyHutConfigClass?: Il2Cpp.Class;
    private methods: GoodyHutMethods = {};
    private configMethods: GoodyHutConfigMethods = {};
    private processedInstances = new Set<string>();
    private isInitialized = false;

    /**
     * Initialize IL2CPP and discover classes/methods automatically
     */
    public async initialize(): Promise<boolean> {
        try {
            console.log("🔍 Auto-discovering GoodyHut classes and methods...");

            // Wait for IL2CPP domain to be ready
            await Il2Cpp.initialize();
            console.log("✅ IL2CPP domain initialized");

            // Get Assembly-CSharp
            this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
            if (!this.assemblyImage) {
                console.log("❌ Failed to get Assembly-CSharp image");
                this.listAvailableAssemblies();
                return false;
            }

            // Auto-discover GoodyHutHelper class
            if (!await this.discoverGoodyHutHelperClass()) {
                return false;
            }

            // Auto-discover GoodyHutHelperConfig class
            if (!await this.discoverGoodyHutConfigClass()) {
                console.log("⚠️ GoodyHutHelperConfig class not found, continuing without config methods");
            }

            // Auto-discover all required methods
            if (!await this.discoverMethods()) {
                return false;
            }

            this.isInitialized = true;
            console.log("🎉 Auto-discovery completed successfully!");
            this.printDiscoveredMethods();
            
            return true;

        } catch (error) {
            console.log(`❌ Initialization failed: ${error}`);
            return false;
        }
    }

    /**
     * Auto-discover GoodyHutHelper class using multiple strategies
     */
    private async discoverGoodyHutHelperClass(): Promise<boolean> {
        const potentialClassNames = [
            "GoodyHutHelper",
            "GoodyHut", 
            "RuinHelper",
            "CollectibleHelper",
            "HutHelper"
        ];

        for (const className of potentialClassNames) {
            try {
                const foundClass = this.assemblyImage!.class(className);
                if (foundClass) {
                    console.log(`✅ Found GoodyHut class: ${className}`);
                    this.goodyHutHelperClass = foundClass;
                    return true;
                }
            } catch (error) {
                // Continue searching
            }
        }

        console.log("❌ GoodyHutHelper class not found with standard names");
        console.log("🔍 Searching for classes with 'Goody' or 'Hut' in name...");
        
        // Fallback: search all classes for ones containing "Goody" or "Hut"
        try {
            const allClasses = this.assemblyImage!.classes;
            for (const cls of allClasses) {
                const name = cls.name.toLowerCase();
                if (name.includes('goody') || name.includes('hut') || name.includes('ruin')) {
                    console.log(`🎯 Found potential class: ${cls.name}`);
                    
                    // Check if it has methods that look like GoodyHutHelper
                    const methods = cls.methods;
                    const hasCanCollect = methods.some(m => m.name === 'CanCollect');
                    const hasFinishCollect = methods.some(m => m.name === 'FinishCollect');
                    
                    if (hasCanCollect && hasFinishCollect) {
                        console.log(`✅ Auto-discovered GoodyHutHelper class: ${cls.name}`);
                        this.goodyHutHelperClass = cls;
                        return true;
                    }
                }
            }
        } catch (error) {
            console.log(`⚠️ Error during class search: ${error}`);
        }

        return false;
    }

    /**
     * Auto-discover GoodyHutHelperConfig class
     */
    private async discoverGoodyHutConfigClass(): Promise<boolean> {
        const potentialConfigNames = [
            "GoodyHutHelperConfig",
            "GoodyHutConfig",
            "HutConfig",
            "RuinConfig"
        ];

        for (const className of potentialConfigNames) {
            try {
                const foundClass = this.assemblyImage!.class(className);
                if (foundClass) {
                    console.log(`✅ Found GoodyHutConfig class: ${className}`);
                    this.goodyHutConfigClass = foundClass;
                    return true;
                }
            } catch (error) {
                // Continue searching
            }
        }

        return false;
    }

    /**
     * Auto-discover all required methods from the classes
     */
    private async discoverMethods(): Promise<boolean> {
        if (!this.goodyHutHelperClass) {
            console.log("❌ Cannot discover methods - GoodyHutHelper class not found");
            return false;
        }

        console.log("🔍 Auto-discovering methods...");

        // Method mapping: what we're looking for -> potential method names
        const methodMappings = {
            canCollect: ['CanCollect', 'canCollect', 'IsCollectable', 'CanBeCollected'],
            finishCollect: ['FinishCollect', 'finishCollect', 'CompleteCollection', 'Collect'],
            config: ['Config', 'config', 'GetConfig', 'getConfig'],
            sellRuins: ['SellRuins', 'sellRuins', 'SellRuin', 'ClearRuins'],
            getRewardType: ['GetRewardType', 'getRewardType', 'RewardType'],
            getRewardAmount: ['GetRewardAmount', 'getRewardAmount', 'RewardAmount'],
            update: ['Update', 'update', 'FixedUpdate']
        };

        const classMethods = this.goodyHutHelperClass.methods;
        let foundMethods = 0;

        for (const [key, potentialNames] of Object.entries(methodMappings)) {
            for (const methodName of potentialNames) {
                const method = classMethods.find(m => m.name === methodName);
                if (method) {
                    (this.methods as any)[key] = method;
                    console.log(`✅ Found method: ${methodName} -> ${key}`);
                    foundMethods++;
                    break;
                }
            }
            
            if (!(this.methods as any)[key]) {
                console.log(`⚠️ Method not found for: ${key}`);
            }
        }

        // Discover config methods if config class is available
        if (this.goodyHutConfigClass) {
            this.discoverConfigMethods();
        }

        console.log(`📊 Discovered ${foundMethods}/${Object.keys(methodMappings).length} methods`);
        
        // We need at least CanCollect and FinishCollect to function
        return this.methods.canCollect !== undefined && this.methods.finishCollect !== undefined;
    }

    /**
     * Discover config-related methods
     */
    private discoverConfigMethods(): void {
        if (!this.goodyHutConfigClass) return;

        const configMethods = this.goodyHutConfigClass.methods;
        
        // Look for cleanUp field getter/setter
        const cleanUpGetter = configMethods.find(m => 
            m.name === 'get_cleanUp' || m.name === 'getCleanUp' || m.name === 'GetCleanUp'
        );
        const cleanUpSetter = configMethods.find(m => 
            m.name === 'set_cleanUp' || m.name === 'setCleanUp' || m.name === 'SetCleanUp'
        );

        if (cleanUpGetter) {
            this.configMethods.getCleanUp = cleanUpGetter;
            console.log("✅ Found cleanUp getter method");
        }
        if (cleanUpSetter) {
            this.configMethods.setCleanUp = cleanUpSetter;
            console.log("✅ Found cleanUp setter method");
        }
    }

    /**
     * Print all discovered methods for debugging
     */
    private printDiscoveredMethods(): void {
        console.log("\n📋 Discovered Methods Summary:");
        console.log("================================");
        
        Object.entries(this.methods).forEach(([key, method]) => {
            if (method) {
                console.log(`✅ ${key}: ${method.name} (${method.address})`);
            }
        });

        if (Object.keys(this.configMethods).length > 0) {
            console.log("\n📋 Config Methods:");
            Object.entries(this.configMethods).forEach(([key, method]) => {
                if (method) {
                    console.log(`✅ ${key}: ${method.name} (${method.address})`);
                }
            });
        }
        console.log("================================\n");
    }

    /**
     * List available assemblies for debugging
     */
    private listAvailableAssemblies(): void {
        try {
            console.log("💡 Available assemblies:");
            const assemblies = Il2Cpp.domain.assemblies;
            assemblies.slice(0, 10).forEach((assembly, index) => {
                console.log(`   ${index}: ${assembly.name}`);
            });
            if (assemblies.length > 10) {
                console.log(`   ... and ${assemblies.length - 10} more`);
            }
        } catch (error) {
            console.log("   Could not enumerate assemblies");
        }
    }

    /**
     * Start the auto-collection process using discovered methods
     */
    public async startAutoCollection(): Promise<void> {
        if (!this.isInitialized) {
            console.log("❌ Cannot start collection - not initialized");
            return;
        }

        console.log("🚀 Starting auto-collection with discovered methods...");

        // Hook the Update method to discover instances
        if (this.methods.update) {
            this.hookUpdateMethod();
        }

        // Hook CanCollect for real-time processing
        if (this.methods.canCollect) {
            this.hookCanCollectMethod();
        }

        console.log("✅ Auto-collection hooks installed");
    }

    /**
     * Hook the Update method to discover GoodyHutHelper instances
     */
    private hookUpdateMethod(): void {
        if (!this.methods.update) return;

        console.log(`🎣 Hooking Update method: ${this.methods.update.name}`);

        this.methods.update.implementation = Il2Cpp.Api.il2cpp_method_get_implementation(this.methods.update);

        Interceptor.attach(this.methods.update.implementation, {
            onEnter: (args) => {
                const thisPtr = args[0];
                const instanceId = thisPtr.toString();

                // Process every 15th call to avoid spam
                if (Math.random() < 0.067) { // ~1/15 chance
                    this.processInstanceIfCollectable(thisPtr, instanceId);
                }
            }
        });
    }

    /**
     * Hook CanCollect method for monitoring
     */
    private hookCanCollectMethod(): void {
        if (!this.methods.canCollect) return;

        console.log(`🎣 Hooking CanCollect method: ${this.methods.canCollect.name}`);

        this.methods.canCollect.implementation = Il2Cpp.Api.il2cpp_method_get_implementation(this.methods.canCollect);

        Interceptor.attach(this.methods.canCollect.implementation, {
            onEnter: (args) => {
                this.lastCanCollectInstance = args[0];
            },
            onLeave: (retval) => {
                const canCollect = retval.toInt32();
                if (canCollect === 1 && this.lastCanCollectInstance) {
                    const instanceId = this.lastCanCollectInstance.toString();
                    if (!this.processedInstances.has(instanceId)) {
                        console.log(`[+] Auto-discovered collectible ruins: ${instanceId}`);
                        this.processInstanceIfCollectable(this.lastCanCollectInstance, instanceId);
                    }
                }
            }
        });
    }

    private lastCanCollectInstance?: NativePointer;

    /**
     * Process a GoodyHutHelper instance if it's collectable
     */
    private async processInstanceIfCollectable(thisPtr: NativePointer, instanceId: string): Promise<void> {
        if (this.processedInstances.has(instanceId)) {
            return;
        }

        try {
            // Check if collectable using discovered method
            if (!this.methods.canCollect) return;

            const canCollectResult = this.methods.canCollect.invoke(thisPtr);
            const canCollect = canCollectResult ? 1 : 0;

            if (canCollect === 1) {
                console.log(`[+] Processing collectible ruins: ${instanceId}`);

                // Set cleanUp flag if possible
                await this.setCleanUpFlag(thisPtr);

                // Execute collection using discovered method
                if (this.methods.finishCollect) {
                    this.methods.finishCollect.invoke(thisPtr);
                    console.log(`[+] FinishCollect() executed on: ${instanceId}`);
                }

                // Mark as processed
                this.processedInstances.add(instanceId);
                console.log(`[+] Successfully processed: ${instanceId} (Total: ${this.processedInstances.size})`);

                // Optional: Sell ruins automatically
                setTimeout(() => {
                    this.sellRuinsIfPossible(thisPtr, instanceId);
                }, 1000);
            }

        } catch (error) {
            console.log(`[-] Error processing instance ${instanceId}: ${error}`);
        }
    }

    /**
     * Set cleanUp flag using discovered config methods
     */
    private async setCleanUpFlag(thisPtr: NativePointer): Promise<void> {
        try {
            if (!this.methods.config) return;

            // Get config object
            const configPtr = this.methods.config.invoke(thisPtr);
            if (!configPtr || configPtr.isNull()) {
                console.log("[-] Config method returned null");
                return;
            }

            // Try to set cleanUp flag using discovered setter
            if (this.configMethods.setCleanUp) {
                this.configMethods.setCleanUp.invoke(configPtr, Il2Cpp.Api.il2cpp_value_box(
                    Il2Cpp.Api.il2cpp_get_boolean_class(),
                    Memory.alloc(1).writeU8(1)
                ));
                console.log("[+] Set cleanUp flag using discovered setter");
            } else {
                // Fallback: try direct field access at known offset
                try {
                    const cleanUpOffset = 0x30;
                    configPtr.add(cleanUpOffset).writeU8(1);
                    console.log("[+] Set cleanUp flag using direct field access");
                } catch (error) {
                    console.log(`[-] Could not set cleanUp flag: ${error}`);
                }
            }

        } catch (error) {
            console.log(`[-] Error setting cleanUp flag: ${error}`);
        }
    }

    /**
     * Sell ruins using discovered method
     */
    private sellRuinsIfPossible(thisPtr: NativePointer, instanceId: string): void {
        try {
            if (this.methods.sellRuins) {
                this.methods.sellRuins.invoke(thisPtr);
                console.log(`[+] SellRuins() executed on: ${instanceId}`);
            }
        } catch (error) {
            console.log(`[-] Error selling ruins for ${instanceId}: ${error}`);
        }
    }

    /**
     * Get current statistics
     */
    public getStats(): object {
        return {
            initialized: this.isInitialized,
            processedInstances: this.processedInstances.size,
            discoveredMethods: Object.keys(this.methods).length,
            discoveredConfigMethods: Object.keys(this.configMethods).length,
            hasCanCollect: !!this.methods.canCollect,
            hasFinishCollect: !!this.methods.finishCollect,
            hasConfig: !!this.methods.config,
            hasSellRuins: !!this.methods.sellRuins
        };
    }
}

// Main execution
Il2Cpp.perform(async () => {
    console.log("🚀 Auto-Discovery GoodyHut Collector Starting...");

    const collector = new AutoDiscoveryGoodyHutCollector();

    // Initialize and start collection
    const initialized = await collector.initialize();
    if (initialized) {
        await collector.startAutoCollection();

        // Export functions for manual control
        (global as any).rpc = {
            exports: {
                getStats: () => collector.getStats(),
                printStats: () => {
                    const stats = collector.getStats();
                    console.log("📊 Auto-Discovery Collector Stats:");
                    console.log(JSON.stringify(stats, null, 2));
                }
            }
        };

        console.log("✅ Auto-Discovery GoodyHut Collector is running!");
        console.log("💡 Use rpc.exports.getStats() to check status");

    } else {
        console.log("❌ Failed to initialize Auto-Discovery Collector");
    }
});
