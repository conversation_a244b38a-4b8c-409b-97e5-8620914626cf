/**
 * Auto-Discovery GoodyHut Collector - Modern JavaScript approach
 * Uses frida-il2cpp-bridge for automatic method discovery instead of manual RVA addresses
 * 
 * Benefits:
 * - Game version independent
 * - Automatic method discovery
 * - No manual RVA address maintenance
 * - Robust error handling
 * 
 * Usage: frida -U -f com.nexonm.dominations.adk -l auto-discovery-goody-hut.js
 */

// Use Il2Cpp.perform instead of Java.perform for IL2CPP games
Il2Cpp.perform(() => {
    console.log("🚀 Auto-Discovery GoodyHut Collector Starting...");

    class AutoDiscoveryGoodyHutCollector {
        constructor() {
            this.assemblyImage = null;
            this.goodyHutHelperClass = null;
            this.goodyHutConfigClass = null;
            this.methods = {};
            this.configMethods = {};
            this.processedInstances = new Set();
            this.isInitialized = false;
        }

        /**
         * Initialize IL2CPP and discover classes/methods automatically
         */
        async initialize() {
            try {
                console.log("🔍 Auto-discovering GoodyHut classes and methods...");

                // Get Assembly-CSharp
                this.assemblyImage = Il2Cpp.domain.assembly("Assembly-CSharp").image;
                if (!this.assemblyImage) {
                    console.log("❌ Failed to get Assembly-CSharp image");
                    this.listAvailableAssemblies();
                    return false;
                }

                // Auto-discover GoodyHutHelper class
                if (!await this.discoverGoodyHutHelperClass()) {
                    return false;
                }

                // Auto-discover GoodyHutHelperConfig class
                if (!await this.discoverGoodyHutConfigClass()) {
                    console.log("⚠️ GoodyHutHelperConfig class not found, continuing without config methods");
                }

                // Auto-discover all required methods
                if (!await this.discoverMethods()) {
                    return false;
                }

                this.isInitialized = true;
                console.log("🎉 Auto-discovery completed successfully!");
                this.printDiscoveredMethods();
                
                return true;

            } catch (error) {
                console.log(`❌ Initialization failed: ${error}`);
                return false;
            }
        }

        /**
         * Auto-discover GoodyHutHelper class using multiple strategies
         */
        async discoverGoodyHutHelperClass() {
            const potentialClassNames = [
                "GoodyHutHelper",
                "GoodyHut", 
                "RuinHelper",
                "CollectibleHelper",
                "HutHelper"
            ];

            for (const className of potentialClassNames) {
                try {
                    const foundClass = this.assemblyImage.class(className);
                    if (foundClass) {
                        console.log(`✅ Found GoodyHut class: ${className}`);
                        this.goodyHutHelperClass = foundClass;
                        return true;
                    }
                } catch (error) {
                    // Continue searching
                }
            }

            console.log("❌ GoodyHutHelper class not found with standard names");
            console.log("🔍 Searching for classes with 'Goody' or 'Hut' in name...");
            
            // Fallback: search all classes for ones containing "Goody" or "Hut"
            try {
                const allClasses = this.assemblyImage.classes;
                for (const cls of allClasses) {
                    const name = cls.name.toLowerCase();
                    if (name.includes('goody') || name.includes('hut') || name.includes('ruin')) {
                        console.log(`🎯 Found potential class: ${cls.name}`);
                        
                        // Check if it has methods that look like GoodyHutHelper
                        const methods = cls.methods;
                        const hasCanCollect = methods.some(m => m.name === 'CanCollect');
                        const hasFinishCollect = methods.some(m => m.name === 'FinishCollect');
                        
                        if (hasCanCollect && hasFinishCollect) {
                            console.log(`✅ Auto-discovered GoodyHutHelper class: ${cls.name}`);
                            this.goodyHutHelperClass = cls;
                            return true;
                        }
                    }
                }
            } catch (error) {
                console.log(`⚠️ Error during class search: ${error}`);
            }

            return false;
        }

        /**
         * Auto-discover GoodyHutHelperConfig class
         */
        async discoverGoodyHutConfigClass() {
            const potentialConfigNames = [
                "GoodyHutHelperConfig",
                "GoodyHutConfig",
                "HutConfig",
                "RuinConfig"
            ];

            for (const className of potentialConfigNames) {
                try {
                    const foundClass = this.assemblyImage.class(className);
                    if (foundClass) {
                        console.log(`✅ Found GoodyHutConfig class: ${className}`);
                        this.goodyHutConfigClass = foundClass;
                        return true;
                    }
                } catch (error) {
                    // Continue searching
                }
            }

            return false;
        }

        /**
         * Auto-discover all required methods from the classes
         */
        async discoverMethods() {
            if (!this.goodyHutHelperClass) {
                console.log("❌ Cannot discover methods - GoodyHutHelper class not found");
                return false;
            }

            console.log("🔍 Auto-discovering methods...");

            // Method mapping: what we're looking for -> potential method names
            const methodMappings = {
                canCollect: ['CanCollect', 'canCollect', 'IsCollectable', 'CanBeCollected'],
                finishCollect: ['FinishCollect', 'finishCollect', 'CompleteCollection', 'Collect'],
                config: ['Config', 'config', 'GetConfig', 'getConfig'],
                sellRuins: ['SellRuins', 'sellRuins', 'SellRuin', 'ClearRuins'],
                getRewardType: ['GetRewardType', 'getRewardType', 'RewardType'],
                getRewardAmount: ['GetRewardAmount', 'getRewardAmount', 'RewardAmount'],
                update: ['Update', 'update', 'FixedUpdate']
            };

            const classMethods = this.goodyHutHelperClass.methods;
            let foundMethods = 0;

            for (const [key, potentialNames] of Object.entries(methodMappings)) {
                for (const methodName of potentialNames) {
                    const method = classMethods.find(m => m.name === methodName);
                    if (method) {
                        this.methods[key] = method;
                        console.log(`✅ Found method: ${methodName} -> ${key}`);
                        foundMethods++;
                        break;
                    }
                }
                
                if (!this.methods[key]) {
                    console.log(`⚠️ Method not found for: ${key}`);
                }
            }

            // Discover config methods if config class is available
            if (this.goodyHutConfigClass) {
                this.discoverConfigMethods();
            }

            console.log(`📊 Discovered ${foundMethods}/${Object.keys(methodMappings).length} methods`);
            
            // We need at least CanCollect and FinishCollect to function
            return this.methods.canCollect !== undefined && this.methods.finishCollect !== undefined;
        }

        /**
         * Discover config-related methods
         */
        discoverConfigMethods() {
            if (!this.goodyHutConfigClass) return;

            const configMethods = this.goodyHutConfigClass.methods;
            
            // Look for cleanUp field getter/setter
            const cleanUpGetter = configMethods.find(m => 
                m.name === 'get_cleanUp' || m.name === 'getCleanUp' || m.name === 'GetCleanUp'
            );
            const cleanUpSetter = configMethods.find(m => 
                m.name === 'set_cleanUp' || m.name === 'setCleanUp' || m.name === 'SetCleanUp'
            );

            if (cleanUpGetter) {
                this.configMethods.getCleanUp = cleanUpGetter;
                console.log("✅ Found cleanUp getter method");
            }
            if (cleanUpSetter) {
                this.configMethods.setCleanUp = cleanUpSetter;
                console.log("✅ Found cleanUp setter method");
            }
        }

        /**
         * Print all discovered methods for debugging
         */
        printDiscoveredMethods() {
            console.log("\n📋 Discovered Methods Summary:");
            console.log("================================");
            
            Object.entries(this.methods).forEach(([key, method]) => {
                if (method) {
                    console.log(`✅ ${key}: ${method.name} (${method.address})`);
                }
            });

            if (Object.keys(this.configMethods).length > 0) {
                console.log("\n📋 Config Methods:");
                Object.entries(this.configMethods).forEach(([key, method]) => {
                    if (method) {
                        console.log(`✅ ${key}: ${method.name} (${method.address})`);
                    }
                });
            }
            console.log("================================\n");
        }

        /**
         * List available assemblies for debugging
         */
        listAvailableAssemblies() {
            try {
                console.log("💡 Available assemblies:");
                const assemblies = Il2Cpp.domain.assemblies;
                assemblies.slice(0, 10).forEach((assembly, index) => {
                    console.log(`   ${index}: ${assembly.name}`);
                });
                if (assemblies.length > 10) {
                    console.log(`   ... and ${assemblies.length - 10} more`);
                }
            } catch (error) {
                console.log("   Could not enumerate assemblies");
            }
        }
