📦
86701 /src/entitycontroller-hook.js
✄
var d=function(e,t,a,n){var s=arguments.length,r=s<3?t:n===null?n=Object.getOwnPropertyDescriptor(t,a):n,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")r=Reflect.decorate(e,t,a,n);else for(var o=e.length-1;o>=0;o--)(i=e[o])&&(r=(s<3?i(r):s>3?i(t,a,r):i(t,a))||r);return s>3&&r&&Object.defineProperty(t,a,r),r},h;(function(e){e.application={get dataPath(){return t("get_persistentDataPath")},get identifier(){return t("get_identifier")??t("get_bundleIdentifier")??Process.mainModule.name},get version(){return t("get_version")??q(e.module).toString(16)}},w(e,"unityVersion",()=>{try{let n=e.$config.unityVersion??t("get_unityVersion");if(n!=null)return n}catch{}let a="69 6c 32 63 70 70";for(let n of e.module.enumerateRanges("r--").concat(Process.getRangeByAddress(e.module.base)))for(let{address:s}of Memory.scanSync(n.base,n.size,a)){for(;s.readU8()!=0;)s=s.sub(1);let r=U.find(s.add(1).readCString());if(r!=null)return r}p("couldn't determine the Unity version, please specify it manually")},c),w(e,"unityVersionIsBelow201830",()=>U.lt(e.unityVersion,"2018.3.0"),c),w(e,"unityVersionIsBelow202120",()=>U.lt(e.unityVersion,"2021.2.0"),c);function t(a){let n=e.exports.resolveInternalCall(Memory.allocUtf8String("UnityEngine.Application::"+a)),s=new NativeFunction(n,"pointer",[]);return s.isNull()?null:new e.String(s()).asNullable()?.content??null}})(h||(h={}));var h;(function(e){function t(a,n){let s={int8:"System.SByte",uint8:"System.Byte",int16:"System.Int16",uint16:"System.UInt16",int32:"System.Int32",uint32:"System.UInt32",int64:"System.Int64",uint64:"System.UInt64",char:"System.Char",intptr:"System.IntPtr",uintptr:"System.UIntPtr"},r=typeof a=="boolean"?"System.Boolean":typeof a=="number"?s[n??"int32"]:a instanceof Int64?"System.Int64":a instanceof UInt64?"System.UInt64":a instanceof NativePointer?s[n??"intptr"]:p(`Cannot create boxed primitive using value of type '${typeof a}'`),i=e.corlib.class(r??p(`Unknown primitive type name '${n}'`)).alloc();return(i.tryField("m_value")??i.tryField("_pointer")??p(`Could not find primitive field in class '${r}'`)).value=a,i}e.boxed=t})(h||(h={}));var h;(function(e){e.$config={moduleName:void 0,unityVersion:void 0,exports:void 0}})(h||(h={}));var h;(function(e){function t(i,o){i=i??`${e.application.identifier}_${e.application.version}.cs`,o=o??e.application.dataPath??Process.getCurrentDir(),s(o);let l=`${o}/${i}`,u=new File(l,"w");for(let g of e.domain.assemblies){A(`dumping ${g.name}...`);for(let y of g.image.classes)u.write(`${y}

`)}u.flush(),u.close(),R(`dump saved to ${l}`),r()}e.dump=t;function a(i,o=!1){i=i??`${e.application.dataPath??Process.getCurrentDir()}/${e.application.identifier}_${e.application.version}`,!o&&n(i)&&p(`directory ${i} already exists - pass ignoreAlreadyExistingDirectory = true to skip this check`);for(let l of e.domain.assemblies){A(`dumping ${l.name}...`);let u=`${i}/${l.name.replaceAll(".","/")}.cs`;s(u.substring(0,u.lastIndexOf("/")));let g=new File(u,"w");for(let y of l.image.classes)g.write(`${y}

`);g.flush(),g.close()}R(`dump saved to ${i}`),r()}e.dumpTree=a;function n(i){return e.corlib.class("System.IO.Directory").method("Exists").invoke(e.string(i))}function s(i){e.corlib.class("System.IO.Directory").method("CreateDirectory").invoke(e.string(i))}function r(){k("this api will be removed in a future release, please use `npx frida-il2cpp-bridge dump` instead")}})(h||(h={}));var h;(function(e){function t(a="current"){let n=e.exports.threadGetCurrent();return Interceptor.attach(e.module.getExportByName("__cxa_throw"),function(s){a=="current"&&!e.exports.threadGetCurrent().equals(n)||A(new e.Object(s[0].readPointer()))})}e.installExceptionListener=t})(h||(h={}));var h;(function(e){e.exports={get alloc(){return t("il2cpp_alloc","pointer",["size_t"])},get arrayGetLength(){return t("il2cpp_array_length","uint32",["pointer"])},get arrayNew(){return t("il2cpp_array_new","pointer",["pointer","uint32"])},get assemblyGetImage(){return t("il2cpp_assembly_get_image","pointer",["pointer"])},get classForEach(){return t("il2cpp_class_for_each","void",["pointer","pointer"])},get classFromName(){return t("il2cpp_class_from_name","pointer",["pointer","pointer","pointer"])},get classFromObject(){return t("il2cpp_class_from_system_type","pointer",["pointer"])},get classGetArrayClass(){return t("il2cpp_array_class_get","pointer",["pointer","uint32"])},get classGetArrayElementSize(){return t("il2cpp_class_array_element_size","int",["pointer"])},get classGetAssemblyName(){return t("il2cpp_class_get_assemblyname","pointer",["pointer"])},get classGetBaseType(){return t("il2cpp_class_enum_basetype","pointer",["pointer"])},get classGetDeclaringType(){return t("il2cpp_class_get_declaring_type","pointer",["pointer"])},get classGetElementClass(){return t("il2cpp_class_get_element_class","pointer",["pointer"])},get classGetFieldFromName(){return t("il2cpp_class_get_field_from_name","pointer",["pointer","pointer"])},get classGetFields(){return t("il2cpp_class_get_fields","pointer",["pointer","pointer"])},get classGetFlags(){return t("il2cpp_class_get_flags","int",["pointer"])},get classGetImage(){return t("il2cpp_class_get_image","pointer",["pointer"])},get classGetInstanceSize(){return t("il2cpp_class_instance_size","int32",["pointer"])},get classGetInterfaces(){return t("il2cpp_class_get_interfaces","pointer",["pointer","pointer"])},get classGetMethodFromName(){return t("il2cpp_class_get_method_from_name","pointer",["pointer","pointer","int"])},get classGetMethods(){return t("il2cpp_class_get_methods","pointer",["pointer","pointer"])},get classGetName(){return t("il2cpp_class_get_name","pointer",["pointer"])},get classGetNamespace(){return t("il2cpp_class_get_namespace","pointer",["pointer"])},get classGetNestedClasses(){return t("il2cpp_class_get_nested_types","pointer",["pointer","pointer"])},get classGetParent(){return t("il2cpp_class_get_parent","pointer",["pointer"])},get classGetStaticFieldData(){return t("il2cpp_class_get_static_field_data","pointer",["pointer"])},get classGetValueTypeSize(){return t("il2cpp_class_value_size","int32",["pointer","pointer"])},get classGetType(){return t("il2cpp_class_get_type","pointer",["pointer"])},get classHasReferences(){return t("il2cpp_class_has_references","bool",["pointer"])},get classInitialize(){return t("il2cpp_runtime_class_init","void",["pointer"])},get classIsAbstract(){return t("il2cpp_class_is_abstract","bool",["pointer"])},get classIsAssignableFrom(){return t("il2cpp_class_is_assignable_from","bool",["pointer","pointer"])},get classIsBlittable(){return t("il2cpp_class_is_blittable","bool",["pointer"])},get classIsEnum(){return t("il2cpp_class_is_enum","bool",["pointer"])},get classIsGeneric(){return t("il2cpp_class_is_generic","bool",["pointer"])},get classIsInflated(){return t("il2cpp_class_is_inflated","bool",["pointer"])},get classIsInterface(){return t("il2cpp_class_is_interface","bool",["pointer"])},get classIsSubclassOf(){return t("il2cpp_class_is_subclass_of","bool",["pointer","pointer","bool"])},get classIsValueType(){return t("il2cpp_class_is_valuetype","bool",["pointer"])},get domainGetAssemblyFromName(){return t("il2cpp_domain_assembly_open","pointer",["pointer","pointer"])},get domainGet(){return t("il2cpp_domain_get","pointer",[])},get domainGetAssemblies(){return t("il2cpp_domain_get_assemblies","pointer",["pointer","pointer"])},get fieldGetClass(){return t("il2cpp_field_get_parent","pointer",["pointer"])},get fieldGetFlags(){return t("il2cpp_field_get_flags","int",["pointer"])},get fieldGetName(){return t("il2cpp_field_get_name","pointer",["pointer"])},get fieldGetOffset(){return t("il2cpp_field_get_offset","int32",["pointer"])},get fieldGetStaticValue(){return t("il2cpp_field_static_get_value","void",["pointer","pointer"])},get fieldGetType(){return t("il2cpp_field_get_type","pointer",["pointer"])},get fieldSetStaticValue(){return t("il2cpp_field_static_set_value","void",["pointer","pointer"])},get free(){return t("il2cpp_free","void",["pointer"])},get gcCollect(){return t("il2cpp_gc_collect","void",["int"])},get gcCollectALittle(){return t("il2cpp_gc_collect_a_little","void",[])},get gcDisable(){return t("il2cpp_gc_disable","void",[])},get gcEnable(){return t("il2cpp_gc_enable","void",[])},get gcGetHeapSize(){return t("il2cpp_gc_get_heap_size","int64",[])},get gcGetMaxTimeSlice(){return t("il2cpp_gc_get_max_time_slice_ns","int64",[])},get gcGetUsedSize(){return t("il2cpp_gc_get_used_size","int64",[])},get gcHandleGetTarget(){return t("il2cpp_gchandle_get_target","pointer",["uint32"])},get gcHandleFree(){return t("il2cpp_gchandle_free","void",["uint32"])},get gcHandleNew(){return t("il2cpp_gchandle_new","uint32",["pointer","bool"])},get gcHandleNewWeakRef(){return t("il2cpp_gchandle_new_weakref","uint32",["pointer","bool"])},get gcIsDisabled(){return t("il2cpp_gc_is_disabled","bool",[])},get gcIsIncremental(){return t("il2cpp_gc_is_incremental","bool",[])},get gcSetMaxTimeSlice(){return t("il2cpp_gc_set_max_time_slice_ns","void",["int64"])},get gcStartIncrementalCollection(){return t("il2cpp_gc_start_incremental_collection","void",[])},get gcStartWorld(){return t("il2cpp_start_gc_world","void",[])},get gcStopWorld(){return t("il2cpp_stop_gc_world","void",[])},get getCorlib(){return t("il2cpp_get_corlib","pointer",[])},get imageGetAssembly(){return t("il2cpp_image_get_assembly","pointer",["pointer"])},get imageGetClass(){return t("il2cpp_image_get_class","pointer",["pointer","uint"])},get imageGetClassCount(){return t("il2cpp_image_get_class_count","uint32",["pointer"])},get imageGetName(){return t("il2cpp_image_get_name","pointer",["pointer"])},get initialize(){return t("il2cpp_init","void",["pointer"])},get livenessAllocateStruct(){return t("il2cpp_unity_liveness_allocate_struct","pointer",["pointer","int","pointer","pointer","pointer"])},get livenessCalculationBegin(){return t("il2cpp_unity_liveness_calculation_begin","pointer",["pointer","int","pointer","pointer","pointer","pointer"])},get livenessCalculationEnd(){return t("il2cpp_unity_liveness_calculation_end","void",["pointer"])},get livenessCalculationFromStatics(){return t("il2cpp_unity_liveness_calculation_from_statics","void",["pointer"])},get livenessFinalize(){return t("il2cpp_unity_liveness_finalize","void",["pointer"])},get livenessFreeStruct(){return t("il2cpp_unity_liveness_free_struct","void",["pointer"])},get memorySnapshotCapture(){return t("il2cpp_capture_memory_snapshot","pointer",[])},get memorySnapshotFree(){return t("il2cpp_free_captured_memory_snapshot","void",["pointer"])},get memorySnapshotGetClasses(){return t("il2cpp_memory_snapshot_get_classes","pointer",["pointer","pointer"])},get memorySnapshotGetObjects(){return t("il2cpp_memory_snapshot_get_objects","pointer",["pointer","pointer"])},get methodGetClass(){return t("il2cpp_method_get_class","pointer",["pointer"])},get methodGetFlags(){return t("il2cpp_method_get_flags","uint32",["pointer","pointer"])},get methodGetName(){return t("il2cpp_method_get_name","pointer",["pointer"])},get methodGetObject(){return t("il2cpp_method_get_object","pointer",["pointer","pointer"])},get methodGetParameterCount(){return t("il2cpp_method_get_param_count","uint8",["pointer"])},get methodGetParameterName(){return t("il2cpp_method_get_param_name","pointer",["pointer","uint32"])},get methodGetParameters(){return t("il2cpp_method_get_parameters","pointer",["pointer","pointer"])},get methodGetParameterType(){return t("il2cpp_method_get_param","pointer",["pointer","uint32"])},get methodGetReturnType(){return t("il2cpp_method_get_return_type","pointer",["pointer"])},get methodIsGeneric(){return t("il2cpp_method_is_generic","bool",["pointer"])},get methodIsInflated(){return t("il2cpp_method_is_inflated","bool",["pointer"])},get methodIsInstance(){return t("il2cpp_method_is_instance","bool",["pointer"])},get monitorEnter(){return t("il2cpp_monitor_enter","void",["pointer"])},get monitorExit(){return t("il2cpp_monitor_exit","void",["pointer"])},get monitorPulse(){return t("il2cpp_monitor_pulse","void",["pointer"])},get monitorPulseAll(){return t("il2cpp_monitor_pulse_all","void",["pointer"])},get monitorTryEnter(){return t("il2cpp_monitor_try_enter","bool",["pointer","uint32"])},get monitorTryWait(){return t("il2cpp_monitor_try_wait","bool",["pointer","uint32"])},get monitorWait(){return t("il2cpp_monitor_wait","void",["pointer"])},get objectGetClass(){return t("il2cpp_object_get_class","pointer",["pointer"])},get objectGetVirtualMethod(){return t("il2cpp_object_get_virtual_method","pointer",["pointer","pointer"])},get objectInitialize(){return t("il2cpp_runtime_object_init_exception","void",["pointer","pointer"])},get objectNew(){return t("il2cpp_object_new","pointer",["pointer"])},get objectGetSize(){return t("il2cpp_object_get_size","uint32",["pointer"])},get objectUnbox(){return t("il2cpp_object_unbox","pointer",["pointer"])},get resolveInternalCall(){return t("il2cpp_resolve_icall","pointer",["pointer"])},get stringGetChars(){return t("il2cpp_string_chars","pointer",["pointer"])},get stringGetLength(){return t("il2cpp_string_length","int32",["pointer"])},get stringNew(){return t("il2cpp_string_new","pointer",["pointer"])},get valueTypeBox(){return t("il2cpp_value_box","pointer",["pointer","pointer"])},get threadAttach(){return t("il2cpp_thread_attach","pointer",["pointer"])},get threadDetach(){return t("il2cpp_thread_detach","void",["pointer"])},get threadGetAttachedThreads(){return t("il2cpp_thread_get_all_attached_threads","pointer",["pointer"])},get threadGetCurrent(){return t("il2cpp_thread_current","pointer",[])},get threadIsVm(){return t("il2cpp_is_vm_thread","bool",["pointer"])},get typeEquals(){return t("il2cpp_type_equals","bool",["pointer","pointer"])},get typeGetClass(){return t("il2cpp_class_from_type","pointer",["pointer"])},get typeGetName(){return t("il2cpp_type_get_name","pointer",["pointer"])},get typeGetObject(){return t("il2cpp_type_get_object","pointer",["pointer"])},get typeGetTypeEnum(){return t("il2cpp_type_get_type","int",["pointer"])}},H(e.exports,c),w(e,"memorySnapshotExports",()=>new CModule(`#include <stdint.h>
#include <string.h>

typedef struct Il2CppManagedMemorySnapshot Il2CppManagedMemorySnapshot;
typedef struct Il2CppMetadataType Il2CppMetadataType;

struct Il2CppManagedMemorySnapshot
{
  struct Il2CppManagedHeap
  {
    uint32_t section_count;
    void * sections;
  } heap;
  struct Il2CppStacks
  {
    uint32_t stack_count;
    void * stacks;
  } stacks;
  struct Il2CppMetadataSnapshot
  {
    uint32_t type_count;
    Il2CppMetadataType * types;
  } metadata_snapshot;
  struct Il2CppGCHandles
  {
    uint32_t tracked_object_count;
    void ** pointers_to_objects;
  } gc_handles;
  struct Il2CppRuntimeInformation
  {
    uint32_t pointer_size;
    uint32_t object_header_size;
    uint32_t array_header_size;
    uint32_t array_bounds_offset_in_header;
    uint32_t array_size_offset_in_header;
    uint32_t allocation_granularity;
  } runtime_information;
  void * additional_user_information;
};

struct Il2CppMetadataType
{
  uint32_t flags;
  void * fields;
  uint32_t field_count;
  uint32_t statics_size;
  uint8_t * statics;
  uint32_t base_or_element_type_index;
  char * name;
  const char * assembly_name;
  uint64_t type_info_address;
  uint32_t size;
};

uintptr_t
il2cpp_memory_snapshot_get_classes (
    const Il2CppManagedMemorySnapshot * snapshot, Il2CppMetadataType ** iter)
{
  const int zero = 0;
  const void * null = 0;

  if (iter != NULL && snapshot->metadata_snapshot.type_count > zero)
  {
    if (*iter == null)
    {
      *iter = snapshot->metadata_snapshot.types;
      return (uintptr_t) (*iter)->type_info_address;
    }
    else
    {
      Il2CppMetadataType * metadata_type = *iter + 1;

      if (metadata_type < snapshot->metadata_snapshot.types +
                              snapshot->metadata_snapshot.type_count)
      {
        *iter = metadata_type;
        return (uintptr_t) (*iter)->type_info_address;
      }
    }
  }
  return 0;
}

void **
il2cpp_memory_snapshot_get_objects (
    const Il2CppManagedMemorySnapshot * snapshot, uint32_t * size)
{
  *size = snapshot->gc_handles.tracked_object_count;
  return snapshot->gc_handles.pointers_to_objects;
}
`),c);function t(a,n,s){let r=e.$config.exports?.[a]?.()??e.module.findExportByName(a)??e.memorySnapshotExports[a],i=new NativeFunction(r??NULL,n,s);return i.isNull()?new Proxy(i,{get(o,l){let u=o[l];return typeof u=="function"?u.bind(o):u},apply(){r==null?p(`couldn't resolve export ${a}`):r.isNull()&&p(`export ${a} points to NULL IL2CPP library has likely been stripped, obfuscated, or customized`)}}):i}})(h||(h={}));var h;(function(e){function t(n){return s=>s instanceof e.Class?n.isAssignableFrom(s):n.isAssignableFrom(s.class)}e.is=t;function a(n){return s=>s instanceof e.Class?s.equals(n):s.class.equals(n)}e.isExactly=a})(h||(h={}));var h;(function(e){e.gc={get heapSize(){return e.exports.gcGetHeapSize()},get isEnabled(){return!e.exports.gcIsDisabled()},get isIncremental(){return!!e.exports.gcIsIncremental()},get maxTimeSlice(){return e.exports.gcGetMaxTimeSlice()},get usedHeapSize(){return e.exports.gcGetUsedSize()},set isEnabled(t){t?e.exports.gcEnable():e.exports.gcDisable()},set maxTimeSlice(t){e.exports.gcSetMaxTimeSlice(t)},choose(t){let a=[],n=(r,i)=>{for(let o=0;o<i;o++)a.push(new e.Object(r.add(o*Process.pointerSize).readPointer()))},s=new NativeCallback(n,"void",["pointer","int","pointer"]);if(e.unityVersionIsBelow202120){let r=new NativeCallback(()=>{},"void",[]),i=e.exports.livenessCalculationBegin(t,0,s,NULL,r,r);e.exports.livenessCalculationFromStatics(i),e.exports.livenessCalculationEnd(i)}else{let r=(l,u)=>!l.isNull()&&u.compare(0)==0?(e.free(l),NULL):e.alloc(u),i=new NativeCallback(r,"pointer",["pointer","size_t","pointer"]);this.stopWorld();let o=e.exports.livenessAllocateStruct(t,0,s,NULL,i);e.exports.livenessCalculationFromStatics(o),e.exports.livenessFinalize(o),this.startWorld(),e.exports.livenessFreeStruct(o)}return a},collect(t){e.exports.gcCollect(t<0?0:t>2?2:t)},collectALittle(){e.exports.gcCollectALittle()},startWorld(){return e.exports.gcStartWorld()},startIncrementalCollection(){return e.exports.gcStartIncrementalCollection()},stopWorld(){return e.exports.gcStopWorld()}}})(h||(h={}));var P;(function(e){w(e,"apiLevel",()=>{let a=t("ro.build.version.sdk");return a?parseInt(a):null},c);function t(a){let n=Process.findModuleByName("libc.so")?.findExportByName("__system_property_get");if(n){let s=new NativeFunction(n,"void",["pointer","pointer"]),r=Memory.alloc(92).writePointer(NULL);return s(Memory.allocUtf8String(a),r),r.readCString()??void 0}}})(P||(P={}));function p(e){let t=new Error(e);throw t.name="Il2CppError",t.stack=t.stack?.replace(/^(Il2Cpp)?Error/,"\x1B[0m\x1B[38;5;9mil2cpp\x1B[0m")?.replace(/\n    at (.+) \((.+):(.+)\)/,"\x1B[3m\x1B[2m")?.concat("\x1B[0m"),t}function k(e){globalThis.console.log(`\x1B[38;5;11mil2cpp\x1B[0m: ${e}`)}function R(e){globalThis.console.log(`\x1B[38;5;10mil2cpp\x1B[0m: ${e}`)}function A(e){globalThis.console.log(`\x1B[38;5;12mil2cpp\x1B[0m: ${e}`)}function H(e,t,a=Object.getOwnPropertyDescriptors(e)){for(let n in a)a[n]=t(e,n,a[n]);return Object.defineProperties(e,a),e}function w(e,t,a,n){globalThis.Object.defineProperty(e,t,n?.(e,t,{get:a,configurable:!0})??{get:a,configurable:!0})}function z(e){let t=3735928559,a=1103547991;for(let n=0,s;n<e.length;n++)s=e.charCodeAt(n),t=Math.imul(t^s,2654435761),a=Math.imul(a^s,1597334677);return t=Math.imul(t^t>>>16,2246822507),t^=Math.imul(a^a>>>13,3266489909),a=Math.imul(a^a>>>16,2246822507),a^=Math.imul(t^t>>>13,3266489909),4294967296*(2097151&a)+(t>>>0)}function q(e){return z(e.enumerateExports().sort((t,a)=>t.name.localeCompare(a.name)).map(t=>t.name+t.address.sub(e.base)).join(""))}function c(e,t,a){let n=a.get;if(!n)throw new Error("@lazy can only be applied to getter accessors");return a.get=function(){let s=n.call(this);return Object.defineProperty(this,t,{value:s,configurable:a.configurable,enumerable:a.enumerable,writable:!1}),s},a}var v=class{handle;constructor(t){t instanceof NativePointer?this.handle=t:this.handle=t.handle}equals(t){return this.handle.equals(t.handle)}isNull(){return this.handle.isNull()}asNullable(){return this.isNull()?null:this}};function Y(e){return Object.keys(e).reduce((t,a)=>(t[t[a]]=a,t),e)}NativePointer.prototype.offsetOf=function(e,t){t??=512;for(let a=0;t>0?a<t:a<-t;a++)if(e(t>0?this.add(a):this.sub(a)))return a;return null};function M(e){let t=[],a=Memory.alloc(Process.pointerSize),n=e(a);for(;!n.isNull();)t.push(n),n=e(a);return t}function O(e){let t=Memory.alloc(Process.pointerSize),a=e(t);if(a.isNull())return[];let n=new Array(t.readInt());for(let s=0;s<n.length;s++)n[s]=a.add(s*Process.pointerSize).readPointer();return n}function N(e){return new Proxy(e,{cache:new Map,construct(t,a){let n=a[0].toUInt32();return this.cache.has(n)||this.cache.set(n,new t(a[0])),this.cache.get(n)}})}var U;(function(e){let t=/(6\d{3}|20\d{2}|\d)\.(\d)\.(\d{1,2})(?:[abcfp]|rc){0,2}\d?/;function a(i){return i?.match(t)?.[0]}e.find=a;function n(i,o){return r(i,o)>=0}e.gte=n;function s(i,o){return r(i,o)<0}e.lt=s;function r(i,o){let l=i.match(t),u=o.match(t);for(let g=1;g<=3;g++){let y=Number(l?.[g]??-1),m=Number(u?.[g]??-1);if(y>m)return 1;if(y<m)return-1}return 0}})(U||(U={}));var h;(function(e){function t(o=Process.pointerSize){return e.exports.alloc(o)}e.alloc=t;function a(o){return e.exports.free(o)}e.free=a;function n(o,l){switch(l.enumValue){case e.Type.Enum.BOOLEAN:return!!o.readS8();case e.Type.Enum.BYTE:return o.readS8();case e.Type.Enum.UBYTE:return o.readU8();case e.Type.Enum.SHORT:return o.readS16();case e.Type.Enum.USHORT:return o.readU16();case e.Type.Enum.INT:return o.readS32();case e.Type.Enum.UINT:return o.readU32();case e.Type.Enum.CHAR:return o.readU16();case e.Type.Enum.LONG:return o.readS64();case e.Type.Enum.ULONG:return o.readU64();case e.Type.Enum.FLOAT:return o.readFloat();case e.Type.Enum.DOUBLE:return o.readDouble();case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return o.readPointer();case e.Type.Enum.POINTER:return new e.Pointer(o.readPointer(),l.class.baseType);case e.Type.Enum.VALUE_TYPE:return new e.ValueType(o,l);case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:return new e.Object(o.readPointer());case e.Type.Enum.GENERIC_INSTANCE:return l.class.isValueType?new e.ValueType(o,l):new e.Object(o.readPointer());case e.Type.Enum.STRING:return new e.String(o.readPointer());case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(o.readPointer())}p(`couldn't read the value from ${o} using an unhandled or unknown type ${l.name} (${l.enumValue}), please file an issue`)}e.read=n;function s(o,l,u){switch(u.enumValue){case e.Type.Enum.BOOLEAN:return o.writeS8(+l);case e.Type.Enum.BYTE:return o.writeS8(l);case e.Type.Enum.UBYTE:return o.writeU8(l);case e.Type.Enum.SHORT:return o.writeS16(l);case e.Type.Enum.USHORT:return o.writeU16(l);case e.Type.Enum.INT:return o.writeS32(l);case e.Type.Enum.UINT:return o.writeU32(l);case e.Type.Enum.CHAR:return o.writeU16(l);case e.Type.Enum.LONG:return o.writeS64(l);case e.Type.Enum.ULONG:return o.writeU64(l);case e.Type.Enum.FLOAT:return o.writeFloat(l);case e.Type.Enum.DOUBLE:return o.writeDouble(l);case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return o.writePointer(l);case e.Type.Enum.VALUE_TYPE:return Memory.copy(o,l,u.class.valueTypeSize),o;case e.Type.Enum.OBJECT:case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:return l instanceof e.ValueType?(Memory.copy(o,l,u.class.valueTypeSize),o):o.writePointer(l)}p(`couldn't write value ${l} to ${o} using an unhandled or unknown type ${u.name} (${u.enumValue}), please file an issue`)}e.write=s;function r(o,l){if(globalThis.Array.isArray(o)){let u=Memory.alloc(l.class.valueTypeSize),g=l.class.fields.filter(y=>!y.isStatic);for(let y=0;y<g.length;y++){let m=r(o[y],g[y].type);s(u.add(g[y].offset).sub(e.Object.headerSize),m,g[y].type)}return new e.ValueType(u,l)}else if(o instanceof NativePointer){if(l.isByReference)return new e.Reference(o,l);switch(l.enumValue){case e.Type.Enum.POINTER:return new e.Pointer(o,l.class.baseType);case e.Type.Enum.STRING:return new e.String(o);case e.Type.Enum.CLASS:case e.Type.Enum.GENERIC_INSTANCE:case e.Type.Enum.OBJECT:return new e.Object(o);case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return new e.Array(o);default:return o}}else return l.enumValue==e.Type.Enum.BOOLEAN?!!o:l.enumValue==e.Type.Enum.VALUE_TYPE&&l.class.isEnum?r([o],l):o}e.fromFridaValue=r;function i(o){if(typeof o=="boolean")return+o;if(o instanceof e.ValueType){if(o.type.class.isEnum)return o.field("value__").value;{let l=o.type.class.fields.filter(u=>!u.isStatic).map(u=>i(u.bind(o).value));return l.length==0?[0]:l}}else return o}e.toFridaValue=i})(h||(h={}));var h;(function(e){w(e,"module",()=>a()??p("Could not find IL2CPP module"));async function t(s=!1){let r=a()??await new Promise(i=>{let[o,l]=n(),u=setTimeout(()=>{k(`after 10 seconds, IL2CPP module '${o}' has not been loaded yet, is the app running?`)},1e4),g=Process.attachModuleObserver({onAdded(y){(y.name==o||l&&y.name==l)&&(clearTimeout(u),setImmediate(()=>{i(y),g.detach()}))}})});return Reflect.defineProperty(e,"module",{value:r}),e.exports.getCorlib().isNull()?await new Promise(i=>{let o=Interceptor.attach(e.exports.initialize,{onLeave(){o.detach(),s?i(!0):setImmediate(()=>i(!1))}})}):!1}e.initialize=t;function a(){let[s,r]=n();return Process.findModuleByName(s)??Process.findModuleByName(r??s)??(Process.platform=="darwin"?Process.findModuleByAddress(DebugSymbol.fromName("il2cpp_init").address):void 0)??void 0}function n(){if(e.$config.moduleName)return[e.$config.moduleName];switch(Process.platform){case"linux":return[P.apiLevel?"libil2cpp.so":"GameAssembly.so"];case"windows":return["GameAssembly.dll"];case"darwin":return["UnityFramework","GameAssembly.dylib"]}p(`${Process.platform} is not supported yet`)}})(h||(h={}));var h;(function(e){async function t(a,n="bind"){let s=null;try{let r=await e.initialize(n=="main");if(n=="main"&&!r)return t(()=>e.mainThread.schedule(a),"free");e.currentThread==null&&(s=e.domain.attach()),n=="bind"&&s!=null&&Script.bindWeak(globalThis,()=>s?.detach());let i=a();return i instanceof Promise?await i:i}catch(r){return Script.nextTick(i=>{throw i},r),Promise.reject(r)}finally{n=="free"&&s!=null&&s.detach()}}e.perform=t})(h||(h={}));var h;(function(e){class t{#e={depth:0,buffer:[],history:new Set,flush:()=>{if(this.#e.depth==0){let r=`
${this.#e.buffer.join(`
`)}
`;if(this.#d)A(r);else{let i=z(r);this.#e.history.has(i)||(this.#e.history.add(i),A(r))}this.#e.buffer.length=0}}};#u=e.mainThread.id;#d=!1;#h;#l=[];#c;#t;#n;#s;#r;#o;#a;#i;constructor(r){this.#h=r}thread(r){return this.#u=r.id,this}verbose(r){return this.#d=r,this}domain(){return this.#c=e.domain,this}assemblies(...r){return this.#t=r,this}classes(...r){return this.#n=r,this}methods(...r){return this.#s=r,this}filterAssemblies(r){return this.#r=r,this}filterClasses(r){return this.#o=r,this}filterMethods(r){return this.#a=r,this}filterParameters(r){return this.#i=r,this}and(){let r=m=>{if(this.#i==null){this.#l.push(m);return}for(let f of m.parameters)if(this.#i(f)){this.#l.push(m);break}},i=m=>{for(let f of m)r(f)},o=m=>{if(this.#a==null){i(m.methods);return}for(let f of m.methods)this.#a(f)&&r(f)},l=m=>{for(let f of m)o(f)},u=m=>{if(this.#o==null){l(m.image.classes);return}for(let f of m.image.classes)this.#o(f)&&o(f)},g=m=>{for(let f of m)u(f)},y=m=>{if(this.#r==null){g(m.assemblies);return}for(let f of m.assemblies)this.#r(f)&&u(f)};return this.#s?i(this.#s):this.#n?l(this.#n):this.#t?g(this.#t):this.#c&&y(this.#c),this.#t=void 0,this.#n=void 0,this.#s=void 0,this.#r=void 0,this.#o=void 0,this.#a=void 0,this.#i=void 0,this}attach(){for(let r of this.#l)if(!r.virtualAddress.isNull())try{this.#h(r,this.#e,this.#u)}catch(i){switch(i.message){case/unable to intercept function at \w+; please file a bug/.exec(i.message)?.input:case"already replaced this function":break;default:throw i}}}}e.Tracer=t;function a(s=!1){let r=()=>(o,l,u)=>{let g=o.relativeVirtualAddress.toString(16).padStart(8,"0");Interceptor.attach(o.virtualAddress,{onEnter(){this.threadId==u&&l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${o.class.type.name}::\x1B[1m${o.name}\x1B[0m\x1B[0m`)},onLeave(){this.threadId==u&&(l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${o.class.type.name}::\x1B[1m${o.name}\x1B[0m\x1B[0m`),l.flush())}})},i=()=>(o,l,u)=>{let g=o.relativeVirtualAddress.toString(16).padStart(8,"0"),y=+!o.isStatic|+e.unityVersionIsBelow201830,m=function(...T){if(this.threadId==u){let $=o.isStatic?void 0:new e.Parameter("this",-1,o.class.type),_=$?[$].concat(o.parameters):o.parameters;l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(l.depth++)}\u250C\u2500\x1B[35m${o.class.type.name}::\x1B[1m${o.name}\x1B[0m\x1B[0m(${_.map(E=>`\x1B[32m${E.name}\x1B[0m = \x1B[31m${e.fromFridaValue(T[E.position+y],E.type)}\x1B[0m`).join(", ")})`)}let b=o.nativeFunction(...T);return this.threadId==u&&(l.buffer.push(`\x1B[2m0x${g}\x1B[0m ${"\u2502 ".repeat(--l.depth)}\u2514\u2500\x1B[33m${o.class.type.name}::\x1B[1m${o.name}\x1B[0m\x1B[0m${b==null?"":` = \x1B[36m${e.fromFridaValue(b,o.returnType)}`}\x1B[0m`),l.flush()),b};o.revert();let f=new NativeCallback(m,o.returnType.fridaAlias,o.fridaSignature);Interceptor.replace(o.virtualAddress,f)};return new e.Tracer(s?i():r())}e.trace=a;function n(s){let r=e.domain.assemblies.flatMap(l=>l.image.classes.flatMap(u=>u.methods.filter(g=>!g.virtualAddress.isNull()))).sort((l,u)=>l.virtualAddress.compare(u.virtualAddress)),i=l=>{let u=0,g=r.length-1;for(;u<=g;){let y=Math.floor((u+g)/2),m=r[y].virtualAddress.compare(l);if(m==0)return r[y];m>0?g=y-1:u=y+1}return r[g]},o=()=>(l,u,g)=>{Interceptor.attach(l.virtualAddress,function(){if(this.threadId==g){let y=globalThis.Thread.backtrace(this.context,s);y.unshift(l.virtualAddress);for(let m of y)if(m.compare(e.module.base)>0&&m.compare(e.module.base.add(e.module.size))<0){let f=i(m);if(f){let T=m.sub(f.virtualAddress);T.compare(4095)<0&&u.buffer.push(`\x1B[2m0x${f.relativeVirtualAddress.toString(16).padStart(8,"0")}\x1B[0m\x1B[2m+0x${T.toString(16).padStart(3,"0")}\x1B[0m ${f.class.type.name}::\x1B[1m${f.name}\x1B[0m`)}}u.flush()}})};return new e.Tracer(o())}e.backtrace=n})(h||(h={}));var h;(function(e){class t extends v{static get headerSize(){return e.corlib.class("System.Array").instanceSize}get elements(){let r=e.string("v").object.method("ToCharArray",0).invoke().handle.offsetOf(i=>i.readS16()==118)??p("couldn't find the elements offset in the native array struct");return w(e.Array.prototype,"elements",function(){return new e.Pointer(this.handle.add(r),this.elementType)},c),this.elements}get elementSize(){return this.elementType.class.arrayElementSize}get elementType(){return this.object.class.type.class.baseType}get length(){return e.exports.arrayGetLength(this)}get object(){return new e.Object(this)}get(s){return(s<0||s>=this.length)&&p(`cannot get element at index ${s} as the array length is ${this.length}`),this.elements.get(s)}set(s,r){(s<0||s>=this.length)&&p(`cannot set element at index ${s} as the array length is ${this.length}`),this.elements.set(s,r)}toString(){return this.isNull()?"null":`[${this.elements.read(this.length,0)}]`}*[Symbol.iterator](){for(let s=0;s<this.length;s++)yield this.elements.get(s)}}d([c],t.prototype,"elementSize",null),d([c],t.prototype,"elementType",null),d([c],t.prototype,"length",null),d([c],t.prototype,"object",null),d([c],t,"headerSize",null),e.Array=t;function a(n,s){let r=typeof s=="number"?s:s.length,i=new e.Array(e.exports.arrayNew(n,r));return globalThis.Array.isArray(s)&&i.elements.write(s),i}e.array=a})(h||(h={}));var h;(function(e){let t=class extends v{get image(){if(e.exports.assemblyGetImage.isNull()){let n=this.object.tryMethod("GetType",1)?.invoke(e.string("<Module>"))?.asNullable()?.tryMethod("get_Module")?.invoke()??this.object.tryMethod("GetModules",1)?.invoke(!1)?.get(0)??p(`couldn't find the runtime module object of assembly ${this.name}`);return new e.Image(n.field("_impl").value)}return new e.Image(e.exports.assemblyGetImage(this))}get name(){return this.image.name.replace(".dll","")}get object(){for(let n of e.domain.object.method("GetAssemblies",1).invoke(!1))if(n.field("_mono_assembly").value.equals(this))return n;p("couldn't find the object of the native assembly struct")}};d([c],t.prototype,"name",null),d([c],t.prototype,"object",null),t=d([N],t),e.Assembly=t})(h||(h={}));var h;(function(e){let t=class extends v{get actualInstanceSize(){let n=e.corlib.class("System.String"),s=n.handle.offsetOf(r=>r.readInt()==n.instanceSize-2)??p("couldn't find the actual instance size offset in the native class struct");return w(e.Class.prototype,"actualInstanceSize",function(){return this.handle.add(s).readS32()},c),this.actualInstanceSize}get arrayClass(){return new e.Class(e.exports.classGetArrayClass(this,1))}get arrayElementSize(){return e.exports.classGetArrayElementSize(this)}get assemblyName(){return e.exports.classGetAssemblyName(this).readUtf8String().replace(".dll","")}get declaringClass(){return new e.Class(e.exports.classGetDeclaringType(this)).asNullable()}get baseType(){return new e.Type(e.exports.classGetBaseType(this)).asNullable()}get elementClass(){return new e.Class(e.exports.classGetElementClass(this)).asNullable()}get fields(){return M(n=>e.exports.classGetFields(this,n)).map(n=>new e.Field(n))}get flags(){return e.exports.classGetFlags(this)}get fullName(){return this.namespace?`${this.namespace}.${this.name}`:this.name}get genericClass(){let n=this.image.tryClass(this.fullName)?.asNullable();return n?.equals(this)?null:n??null}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let n=this.type.object.method("GetGenericArguments").invoke();return globalThis.Array.from(n).map(s=>new e.Class(e.exports.classFromObject(s)))}get hasReferences(){return!!e.exports.classHasReferences(this)}get hasStaticConstructor(){let n=this.tryMethod(".cctor");return n!=null&&!n.virtualAddress.isNull()}get image(){return new e.Image(e.exports.classGetImage(this))}get instanceSize(){return e.exports.classGetInstanceSize(this)}get isAbstract(){return!!e.exports.classIsAbstract(this)}get isBlittable(){return!!e.exports.classIsBlittable(this)}get isEnum(){return!!e.exports.classIsEnum(this)}get isGeneric(){return!!e.exports.classIsGeneric(this)}get isInflated(){return!!e.exports.classIsInflated(this)}get isInterface(){return!!e.exports.classIsInterface(this)}get isStruct(){return this.isValueType&&!this.isEnum}get isValueType(){return!!e.exports.classIsValueType(this)}get interfaces(){return M(n=>e.exports.classGetInterfaces(this,n)).map(n=>new e.Class(n))}get methods(){return M(n=>e.exports.classGetMethods(this,n)).map(n=>new e.Method(n))}get name(){return e.exports.classGetName(this).readUtf8String()}get namespace(){return e.exports.classGetNamespace(this).readUtf8String()||void 0}get nestedClasses(){return M(n=>e.exports.classGetNestedClasses(this,n)).map(n=>new e.Class(n))}get parent(){return new e.Class(e.exports.classGetParent(this)).asNullable()}get pointerClass(){return new e.Class(e.exports.classFromObject(this.type.object.method("MakePointerType").invoke()))}get rank(){let n=0,s=this.name;for(let r=this.name.length-1;r>0;r--){let i=s[r];if(i=="]")n++;else{if(i=="["||n==0)break;if(i==",")n++;else break}}return n}get staticFieldsData(){return e.exports.classGetStaticFieldData(this)}get valueTypeSize(){return e.exports.classGetValueTypeSize(this,NULL)}get type(){return new e.Type(e.exports.classGetType(this))}alloc(){return new e.Object(e.exports.objectNew(this))}field(n){return this.tryField(n)??p(`couldn't find field ${n} in class ${this.type.name}`)}*hierarchy(n){let s=n?.includeCurrent??!0?this:this.parent;for(;s;)yield s,s=s.parent}inflate(...n){this.isGeneric||p(`cannot inflate class ${this.type.name} as it has no generic parameters`),this.generics.length!=n.length&&p(`cannot inflate class ${this.type.name} as it needs ${this.generics.length} generic parameter(s), not ${n.length}`);let s=n.map(o=>o.type.object),r=e.array(e.corlib.class("System.Type"),s),i=this.type.object.method("MakeGenericType",1).invoke(r);return new e.Class(e.exports.classFromObject(i))}initialize(){return e.exports.classInitialize(this),this}isAssignableFrom(n){return!!e.exports.classIsAssignableFrom(this,n)}isSubclassOf(n,s){return!!e.exports.classIsSubclassOf(this,n,+s)}method(n,s=-1){return this.tryMethod(n,s)??p(`couldn't find method ${n} in class ${this.type.name}`)}nested(n){return this.tryNested(n)??p(`couldn't find nested class ${n} in class ${this.type.name}`)}new(){let n=this.alloc(),s=Memory.alloc(Process.pointerSize);e.exports.objectInitialize(n,s);let r=s.readPointer();return r.isNull()||p(new e.Object(r).toString()),n}tryField(n){return new e.Field(e.exports.classGetFieldFromName(this,Memory.allocUtf8String(n))).asNullable()}tryMethod(n,s=-1){return new e.Method(e.exports.classGetMethodFromName(this,Memory.allocUtf8String(n),s)).asNullable()}tryNested(n){return this.nestedClasses.find(s=>s.name==n)}toString(){let n=[this.parent].concat(this.interfaces);return`// ${this.assemblyName}
${this.isEnum?"enum":this.isStruct?"struct":this.isInterface?"interface":"class"} ${this.type.name}${n?` : ${n.map(s=>s?.type.name).join(", ")}`:""}
{
    ${this.fields.join(`
    `)}
    ${this.methods.join(`
    `)}
}`}static enumerate(n){let s=new NativeCallback(r=>n(new e.Class(r)),"void",["pointer","pointer"]);return e.exports.classForEach(s,NULL)}};d([c],t.prototype,"arrayClass",null),d([c],t.prototype,"arrayElementSize",null),d([c],t.prototype,"assemblyName",null),d([c],t.prototype,"declaringClass",null),d([c],t.prototype,"baseType",null),d([c],t.prototype,"elementClass",null),d([c],t.prototype,"fields",null),d([c],t.prototype,"flags",null),d([c],t.prototype,"fullName",null),d([c],t.prototype,"generics",null),d([c],t.prototype,"hasReferences",null),d([c],t.prototype,"hasStaticConstructor",null),d([c],t.prototype,"image",null),d([c],t.prototype,"instanceSize",null),d([c],t.prototype,"isAbstract",null),d([c],t.prototype,"isBlittable",null),d([c],t.prototype,"isEnum",null),d([c],t.prototype,"isGeneric",null),d([c],t.prototype,"isInflated",null),d([c],t.prototype,"isInterface",null),d([c],t.prototype,"isValueType",null),d([c],t.prototype,"interfaces",null),d([c],t.prototype,"methods",null),d([c],t.prototype,"name",null),d([c],t.prototype,"namespace",null),d([c],t.prototype,"nestedClasses",null),d([c],t.prototype,"parent",null),d([c],t.prototype,"pointerClass",null),d([c],t.prototype,"rank",null),d([c],t.prototype,"staticFieldsData",null),d([c],t.prototype,"valueTypeSize",null),d([c],t.prototype,"type",null),t=d([N],t),e.Class=t})(h||(h={}));var h;(function(e){function t(a,n){let s=e.corlib.class("System.Delegate"),r=e.corlib.class("System.MulticastDelegate");s.isAssignableFrom(a)||p(`cannot create a delegate for ${a.type.name} as it's a non-delegate class`),(a.equals(s)||a.equals(r))&&p(`cannot create a delegate for neither ${s.type.name} nor ${r.type.name}, use a subclass instead`);let i=a.alloc(),o=i.handle.toString(),l=i.tryMethod("Invoke")??p(`cannot create a delegate for ${a.type.name}, there is no Invoke method`);i.method(".ctor").invoke(i,l.handle);let u=l.wrap(n);return i.field("method_ptr").value=u,i.field("invoke_impl").value=u,e._callbacksToKeepAlive[o]=u,i}e.delegate=t,e._callbacksToKeepAlive={}})(h||(h={}));var h;(function(e){let t=class extends v{get assemblies(){let n=O(s=>e.exports.domainGetAssemblies(this,s));if(n.length==0){let s=this.object.method("GetAssemblies").overload().invoke();n=globalThis.Array.from(s).map(r=>r.field("_mono_assembly").value)}return n.map(s=>new e.Assembly(s))}get object(){return e.corlib.class("System.AppDomain").method("get_CurrentDomain").invoke()}assembly(n){return this.tryAssembly(n)??p(`couldn't find assembly ${n}`)}attach(){return new e.Thread(e.exports.threadAttach(this))}tryAssembly(n){return new e.Assembly(e.exports.domainGetAssemblyFromName(this,Memory.allocUtf8String(n))).asNullable()}};d([c],t.prototype,"assemblies",null),d([c],t.prototype,"object",null),t=d([N],t),e.Domain=t,w(e,"domain",()=>new e.Domain(e.exports.domainGet()),c)})(h||(h={}));var h;(function(e){class t extends v{get class(){return new e.Class(e.exports.fieldGetClass(this))}get flags(){return e.exports.fieldGetFlags(this)}get isLiteral(){return(this.flags&64)!=0}get isStatic(){return(this.flags&16)!=0}get isThreadStatic(){let n=e.corlib.class("System.AppDomain").field("type_resolve_in_progress").offset;return w(e.Field.prototype,"isThreadStatic",function(){return this.offset==n},c),this.isThreadStatic}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.fieldGetName(this).readUtf8String()}get offset(){return e.exports.fieldGetOffset(this)}get type(){return new e.Type(e.exports.fieldGetType(this))}get value(){this.isStatic||p(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`);let n=Memory.alloc(Process.pointerSize);return e.exports.fieldGetStaticValue(this.handle,n),e.read(n,this.type)}set value(n){this.isStatic||p(`cannot access instance field ${this.class.type.name}::${this.name} from a class, use an object instead`),(this.isThreadStatic||this.isLiteral)&&p(`cannot write the value of field ${this.name} as it's thread static or literal`);let s=n instanceof e.Object&&this.type.class.isValueType?n.unbox():n instanceof v?n.handle:n instanceof NativePointer?n:e.write(Memory.alloc(this.type.class.valueTypeSize),n,this.type);e.exports.fieldSetStaticValue(this.handle,s)}toString(){return`${this.isThreadStatic?"[ThreadStatic] ":""}${this.isStatic?"static ":""}${this.type.name} ${this.name}${this.isLiteral?` = ${this.type.class.isEnum?e.read(this.value.handle,this.type.class.baseType):this.value}`:""};${this.isThreadStatic||this.isLiteral?"":` // 0x${this.offset.toString(16)}`}`}bind(n){this.isStatic&&p(`cannot bind static field ${this.class.type.name}::${this.name} to an instance`);let s=this.offset-(n instanceof e.ValueType?e.Object.headerSize:0);return new Proxy(this,{get(r,i){return i=="value"?e.read(n.handle.add(s),r.type):Reflect.get(r,i)},set(r,i,o){return i=="value"?(e.write(n.handle.add(s),o,r.type),!0):Reflect.set(r,i,o)}})}}d([c],t.prototype,"class",null),d([c],t.prototype,"flags",null),d([c],t.prototype,"isLiteral",null),d([c],t.prototype,"isStatic",null),d([c],t.prototype,"isThreadStatic",null),d([c],t.prototype,"modifier",null),d([c],t.prototype,"name",null),d([c],t.prototype,"offset",null),d([c],t.prototype,"type",null),e.Field=t})(h||(h={}));var h;(function(e){class t{handle;constructor(n){this.handle=n}get target(){return new e.Object(e.exports.gcHandleGetTarget(this.handle)).asNullable()}free(){return e.exports.gcHandleFree(this.handle)}}e.GCHandle=t})(h||(h={}));var h;(function(e){let t=class extends v{get assembly(){return new e.Assembly(e.exports.imageGetAssembly(this))}get classCount(){return e.unityVersionIsBelow201830?this.classes.length:e.exports.imageGetClassCount(this)}get classes(){if(e.unityVersionIsBelow201830){let n=this.assembly.object.method("GetTypes").invoke(!1),s=globalThis.Array.from(n,i=>new e.Class(e.exports.classFromObject(i))),r=this.tryClass("<Module>");return r&&s.unshift(r),s}else return globalThis.Array.from(globalThis.Array(this.classCount),(n,s)=>new e.Class(e.exports.imageGetClass(this,s)))}get name(){return e.exports.imageGetName(this).readUtf8String()}class(n){return this.tryClass(n)??p(`couldn't find class ${n} in assembly ${this.name}`)}tryClass(n){let s=n.lastIndexOf("."),r=Memory.allocUtf8String(s==-1?"":n.slice(0,s)),i=Memory.allocUtf8String(n.slice(s+1));return new e.Class(e.exports.classFromName(this,r,i)).asNullable()}};d([c],t.prototype,"assembly",null),d([c],t.prototype,"classCount",null),d([c],t.prototype,"classes",null),d([c],t.prototype,"name",null),t=d([N],t),e.Image=t,w(e,"corlib",()=>new e.Image(e.exports.getCorlib()),c)})(h||(h={}));var h;(function(e){class t extends v{static capture(){return new e.MemorySnapshot}constructor(s=e.exports.memorySnapshotCapture()){super(s)}get classes(){return M(s=>e.exports.memorySnapshotGetClasses(this,s)).map(s=>new e.Class(s))}get objects(){return O(s=>e.exports.memorySnapshotGetObjects(this,s)).filter(s=>!s.isNull()).map(s=>new e.Object(s))}free(){e.exports.memorySnapshotFree(this)}}d([c],t.prototype,"classes",null),d([c],t.prototype,"objects",null),e.MemorySnapshot=t;function a(n){let s=e.MemorySnapshot.capture(),r=n(s);return s.free(),r}e.memorySnapshot=a})(h||(h={}));var h;(function(e){class t extends v{get class(){return new e.Class(e.exports.methodGetClass(this))}get flags(){return e.exports.methodGetFlags(this,NULL)}get implementationFlags(){let s=Memory.alloc(Process.pointerSize);return e.exports.methodGetFlags(this,s),s.readU32()}get fridaSignature(){let s=[];for(let r of this.parameters)s.push(r.type.fridaAlias);return(!this.isStatic||e.unityVersionIsBelow201830)&&s.unshift("pointer"),this.isInflated&&s.push("pointer"),s}get generics(){if(!this.isGeneric&&!this.isInflated)return[];let s=this.object.method("GetGenericArguments").invoke();return globalThis.Array.from(s).map(r=>new e.Class(e.exports.classFromObject(r)))}get isExternal(){return(this.implementationFlags&4096)!=0}get isGeneric(){return!!e.exports.methodIsGeneric(this)}get isInflated(){return!!e.exports.methodIsInflated(this)}get isStatic(){return!e.exports.methodIsInstance(this)}get isSynchronized(){return(this.implementationFlags&32)!=0}get modifier(){switch(this.flags&7){case 1:return"private";case 2:return"private protected";case 3:return"internal";case 4:return"protected";case 5:return"protected internal";case 6:return"public"}}get name(){return e.exports.methodGetName(this).readUtf8String()}get nativeFunction(){return new NativeFunction(this.virtualAddress,this.returnType.fridaAlias,this.fridaSignature)}get object(){return new e.Object(e.exports.methodGetObject(this,NULL))}get parameterCount(){return e.exports.methodGetParameterCount(this)}get parameters(){return globalThis.Array.from(globalThis.Array(this.parameterCount),(s,r)=>{let i=e.exports.methodGetParameterName(this,r).readUtf8String(),o=e.exports.methodGetParameterType(this,r);return new e.Parameter(i,r,new e.Type(o))})}get relativeVirtualAddress(){return this.virtualAddress.sub(e.module.base)}get returnType(){return new e.Type(e.exports.methodGetReturnType(this))}get virtualAddress(){let s=e.corlib.class("System.Reflection.Module").initialize().field("FilterTypeName").value,r=s.field("method_ptr").value,o=s.field("method").value.offsetOf(l=>l.readPointer().equals(r))??p("couldn't find the virtual address offset in the native method struct");return w(e.Method.prototype,"virtualAddress",function(){return this.handle.add(o).readPointer()},c),e.corlib.class("System.Reflection.Module").method(".cctor").invoke(),this.virtualAddress}set implementation(s){try{Interceptor.replace(this.virtualAddress,this.wrap(s))}catch(r){switch(r.message){case"access violation accessing 0x0":p(`couldn't set implementation for method ${this.name} as it has a NULL virtual address`);case/unable to intercept function at \w+; please file a bug/.exec(r.message)?.input:k(`couldn't set implementation for method ${this.name} as it may be a thunk`);break;case"already replaced this function":k(`couldn't set implementation for method ${this.name} as it has already been replaced by a thunk`);break;default:throw r}}}inflate(...s){if(!this.isGeneric||this.generics.length!=s.length){for(let l of this.overloads())if(l.isGeneric&&l.generics.length==s.length)return l.inflate(...s);p(`could not find inflatable signature of method ${this.name} with ${s.length} generic parameter(s)`)}let r=s.map(l=>l.type.object),i=e.array(e.corlib.class("System.Type"),r),o=this.object.method("MakeGenericMethod",1).invoke(i);return new e.Method(o.field("mhandle").value)}invoke(...s){return this.isStatic||p(`cannot invoke non-static method ${this.name} as it must be invoked throught a Il2Cpp.Object, not a Il2Cpp.Class`),this.invokeRaw(NULL,...s)}invokeRaw(s,...r){let i=r.map(e.toFridaValue);(!this.isStatic||e.unityVersionIsBelow201830)&&i.unshift(s),this.isInflated&&i.push(this.handle);try{let o=this.nativeFunction(...i);return e.fromFridaValue(o,this.returnType)}catch(o){switch(o==null&&p("an unexpected native invocation exception occurred, this is due to parameter types mismatch"),o.message){case"bad argument count":p(`couldn't invoke method ${this.name} as it needs ${this.parameterCount} parameter(s), not ${r.length}`);case"expected a pointer":case"expected number":case"expected array with fields":p(`couldn't invoke method ${this.name} using incorrect parameter types`)}throw o}}overload(...s){return this.tryOverload(...s)??p(`couldn't find overloaded method ${this.name}(${s.map(i=>i instanceof e.Class?i.type.name:i)})`)}*overloads(){for(let s of this.class.hierarchy())for(let r of s.methods)this.name==r.name&&(yield r)}parameter(s){return this.tryParameter(s)??p(`couldn't find parameter ${s} in method ${this.name}`)}revert(){Interceptor.revert(this.virtualAddress),Interceptor.flush()}tryOverload(...s){let r=s.length*1,i=s.length*2,o;e:for(let l of this.overloads()){if(l.parameterCount!=s.length)continue;let u=0,g=0;for(let y of l.parameters){let m=s[g];if(m instanceof e.Class)if(y.type.is(m.type))u+=2;else if(y.type.class.isAssignableFrom(m))u+=1;else continue e;else if(y.type.name==m)u+=2;else continue e;g++}if(!(u<r)){if(u==i)return l;if(o==null||u>o[0])o=[u,l];else if(u==o[0]){let y=0;for(let m of o[1].parameters){if(m.type.class.isAssignableFrom(l.parameters[y].type.class)){o=[u,l];continue e}y++}}}}return o?.[1]}tryParameter(s){return this.parameters.find(r=>r.name==s)}toString(){return`${this.isStatic?"static ":""}${this.returnType.name} ${this.name}${this.generics.length>0?`<${this.generics.map(s=>s.type.name).join(",")}>`:""}(${this.parameters.join(", ")});${this.virtualAddress.isNull()?"":` // 0x${this.relativeVirtualAddress.toString(16).padStart(8,"0")}`}`}bind(s){return this.isStatic&&p(`cannot bind static method ${this.class.type.name}::${this.name} to an instance`),new Proxy(this,{get(r,i,o){switch(i){case"invoke":let l=s instanceof e.ValueType?r.class.isValueType?s.handle.sub(a()?e.Object.headerSize:0):p(`cannot invoke method ${r.class.type.name}::${r.name} against a value type, you must box it first`):r.class.isValueType?s.handle.add(a()?0:e.Object.headerSize):s.handle;return r.invokeRaw.bind(r,l);case"overloads":return function*(){for(let g of r[i]())g.isStatic||(yield g)};case"inflate":case"overload":case"tryOverload":let u=Reflect.get(r,i).bind(o);return function(...g){return u(...g)?.bind(s)}}return Reflect.get(r,i)}})}wrap(s){let r=+!this.isStatic|+e.unityVersionIsBelow201830;return new NativeCallback((...i)=>{let o=this.isStatic?this.class:this.class.isValueType?new e.ValueType(i[0].add(a()?e.Object.headerSize:0),this.class.type):new e.Object(i[0]),l=this.parameters.map((g,y)=>e.fromFridaValue(i[y+r],g.type)),u=s.call(o,...l);return e.toFridaValue(u)},this.returnType.fridaAlias,this.fridaSignature)}}d([c],t.prototype,"class",null),d([c],t.prototype,"flags",null),d([c],t.prototype,"implementationFlags",null),d([c],t.prototype,"fridaSignature",null),d([c],t.prototype,"generics",null),d([c],t.prototype,"isExternal",null),d([c],t.prototype,"isGeneric",null),d([c],t.prototype,"isInflated",null),d([c],t.prototype,"isStatic",null),d([c],t.prototype,"isSynchronized",null),d([c],t.prototype,"modifier",null),d([c],t.prototype,"name",null),d([c],t.prototype,"nativeFunction",null),d([c],t.prototype,"object",null),d([c],t.prototype,"parameterCount",null),d([c],t.prototype,"parameters",null),d([c],t.prototype,"relativeVirtualAddress",null),d([c],t.prototype,"returnType",null),e.Method=t;let a=()=>{let n=e.corlib.class("System.Int64").alloc();n.field("m_value").value=3735928559;let s=n.method("Equals",1).overload(n.class).invokeRaw(n,3735928559);return(a=()=>s)()}})(h||(h={}));var h;(function(e){class t extends v{static get headerSize(){return e.corlib.class("System.Object").instanceSize}get base(){return this.class.parent==null&&p(`class ${this.class.type.name} has no parent`),new Proxy(this,{get(n,s,r){return s=="class"?Reflect.get(n,s).parent:s=="base"?Reflect.getOwnPropertyDescriptor(e.Object.prototype,s).get.bind(r)():Reflect.get(n,s)}})}get class(){return new e.Class(e.exports.objectGetClass(this))}get monitor(){return new e.Object.Monitor(this)}get size(){return e.exports.objectGetSize(this)}field(n){return this.tryField(n)??p(`couldn't find non-static field ${n} in hierarchy of class ${this.class.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??p(`couldn't find non-static method ${n} in hierarchy of class ${this.class.type.name}`)}ref(n){return new e.GCHandle(e.exports.gcHandleNew(this,+n))}virtualMethod(n){return new e.Method(e.exports.objectGetVirtualMethod(this,n)).bind(this)}tryField(n){let s=this.class.tryField(n);if(s?.isStatic){for(let r of this.class.hierarchy({includeCurrent:!1}))for(let i of r.fields)if(i.name==n&&!i.isStatic)return i.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.class.tryMethod(n,s);if(r?.isStatic){for(let i of this.class.hierarchy())for(let o of i.methods)if(o.name==n&&!o.isStatic&&(s<0||o.parameterCount==s))return o.bind(this);return}return r?.bind(this)}toString(){return this.isNull()?"null":this.method("ToString",0).invoke().content??"null"}unbox(){return this.class.isValueType?new e.ValueType(e.exports.objectUnbox(this),this.class.type):p(`couldn't unbox instances of ${this.class.type.name} as they are not value types`)}weakRef(n){return new e.GCHandle(e.exports.gcHandleNewWeakRef(this,+n))}}d([c],t.prototype,"class",null),d([c],t.prototype,"size",null),d([c],t,"headerSize",null),e.Object=t,function(a){class n{handle;constructor(r){this.handle=r}enter(){return e.exports.monitorEnter(this.handle)}exit(){return e.exports.monitorExit(this.handle)}pulse(){return e.exports.monitorPulse(this.handle)}pulseAll(){return e.exports.monitorPulseAll(this.handle)}tryEnter(r){return!!e.exports.monitorTryEnter(this.handle,r)}tryWait(r){return!!e.exports.monitorTryWait(this.handle,r)}wait(){return e.exports.monitorWait(this.handle)}}a.Monitor=n}(t=e.Object||(e.Object={}))})(h||(h={}));var h;(function(e){class t{name;position;type;constructor(n,s,r){this.name=n,this.position=s,this.type=r}toString(){return`${this.type.name} ${this.name}`}}e.Parameter=t})(h||(h={}));var h;(function(e){class t extends v{type;constructor(n,s){super(n),this.type=s}get(n){return e.read(this.handle.add(n*this.type.class.arrayElementSize),this.type)}read(n,s=0){let r=new globalThis.Array(n);for(let i=0;i<n;i++)r[i]=this.get(i+s);return r}set(n,s){e.write(this.handle.add(n*this.type.class.arrayElementSize),s,this.type)}toString(){return this.handle.toString()}write(n,s=0){for(let r=0;r<n.length;r++)this.set(r+s,n[r])}}e.Pointer=t})(h||(h={}));var h;(function(e){class t extends v{type;constructor(s,r){super(s),this.type=r}get value(){return e.read(this.handle,this.type)}set value(s){e.write(this.handle,s,this.type)}toString(){return this.isNull()?"null":`->${this.value}`}}e.Reference=t;function a(n,s){let r=Memory.alloc(Process.pointerSize);switch(typeof n){case"boolean":return new e.Reference(r.writeS8(+n),e.corlib.class("System.Boolean").type);case"number":switch(s?.enumValue){case e.Type.Enum.UBYTE:return new e.Reference(r.writeU8(n),s);case e.Type.Enum.BYTE:return new e.Reference(r.writeS8(n),s);case e.Type.Enum.CHAR:case e.Type.Enum.USHORT:return new e.Reference(r.writeU16(n),s);case e.Type.Enum.SHORT:return new e.Reference(r.writeS16(n),s);case e.Type.Enum.UINT:return new e.Reference(r.writeU32(n),s);case e.Type.Enum.INT:return new e.Reference(r.writeS32(n),s);case e.Type.Enum.ULONG:return new e.Reference(r.writeU64(n),s);case e.Type.Enum.LONG:return new e.Reference(r.writeS64(n),s);case e.Type.Enum.FLOAT:return new e.Reference(r.writeFloat(n),s);case e.Type.Enum.DOUBLE:return new e.Reference(r.writeDouble(n),s)}case"object":if(n instanceof e.ValueType||n instanceof e.Pointer)return new e.Reference(n.handle,n.type);if(n instanceof e.Object)return new e.Reference(r.writePointer(n),n.class.type);if(n instanceof e.String||n instanceof e.Array)return new e.Reference(r.writePointer(n),n.object.class.type);if(n instanceof NativePointer)switch(s?.enumValue){case e.Type.Enum.NUINT:case e.Type.Enum.NINT:return new e.Reference(r.writePointer(n),s)}else{if(n instanceof Int64)return new e.Reference(r.writeS64(n),e.corlib.class("System.Int64").type);if(n instanceof UInt64)return new e.Reference(r.writeU64(n),e.corlib.class("System.UInt64").type)}default:p(`couldn't create a reference to ${n} using an unhandled type ${s?.name}`)}}e.reference=a})(h||(h={}));var h;(function(e){class t extends v{get content(){return e.exports.stringGetChars(this).readUtf16String(this.length)}set content(s){let r=e.string("vfsfitvnm").handle.offsetOf(i=>i.readInt()==9)??p("couldn't find the length offset in the native string struct");globalThis.Object.defineProperty(e.String.prototype,"content",{set(i){e.exports.stringGetChars(this).writeUtf16String(i??""),this.handle.add(r).writeS32(i?.length??0)}}),this.content=s}get length(){return e.exports.stringGetLength(this)}get object(){return new e.Object(this)}toString(){return this.isNull()?"null":`"${this.content}"`}}e.String=t;function a(n){return new e.String(e.exports.stringNew(Memory.allocUtf8String(n??"")))}e.string=a})(h||(h={}));var h;(function(e){class t extends v{get id(){let n=function(){return this.internal.field("thread_id").value.toNumber()};if(Process.platform!="windows"){let s=Process.getCurrentThreadId(),i=ptr(n.apply(e.currentThread)).offsetOf(l=>l.readS32()==s,1024)??p("couldn't find the offset for determining the kernel id of a posix thread"),o=n;n=function(){return ptr(o.apply(this)).add(i).readS32()}}return w(e.Thread.prototype,"id",n,c),this.id}get internal(){return this.object.tryField("internal_thread")?.value??this.object}get isFinalizer(){return!e.exports.threadIsVm(this)}get managedId(){return this.object.method("get_ManagedThreadId").invoke()}get object(){return new e.Object(this)}get staticData(){return this.internal.field("static_data").value}get synchronizationContext(){let s=(this.object.tryMethod("GetMutableExecutionContext")??this.object.method("get_ExecutionContext")).invoke();return(s.tryField("_syncContext")?.value??s.tryMethod("get_SynchronizationContext")?.invoke()??this.tryLocalValue(e.corlib.class("System.Threading.SynchronizationContext")))?.asNullable()??null}detach(){return e.exports.threadDetach(this)}schedule(n){let s=this.synchronizationContext?.tryMethod("Post");return s==null?Process.runOnThread(this.id,n):new Promise(r=>{let i=e.delegate(e.corlib.class("System.Threading.SendOrPostCallback"),()=>{let o=n();setImmediate(()=>r(o))});Script.bindWeak(globalThis,()=>{i.field("method_ptr").value=i.field("invoke_impl").value=e.exports.domainGet}),s.invoke(i,NULL)})}tryLocalValue(n){for(let s=0;s<16;s++){let r=this.staticData.add(s*Process.pointerSize).readPointer();if(!r.isNull()){let i=new e.Object(r.readPointer()).asNullable();if(i?.class?.isSubclassOf(n,!1))return i}}}}d([c],t.prototype,"internal",null),d([c],t.prototype,"isFinalizer",null),d([c],t.prototype,"managedId",null),d([c],t.prototype,"object",null),d([c],t.prototype,"staticData",null),d([c],t.prototype,"synchronizationContext",null),e.Thread=t,w(e,"attachedThreads",()=>{if(e.exports.threadGetAttachedThreads.isNull()){let a=e.currentThread?.handle??p("Current thread is not attached to IL2CPP"),n=a.toMatchPattern(),s=[];for(let r of Process.enumerateRanges("rw-"))if(r.file==null){let i=Memory.scanSync(r.base,r.size,n);if(i.length==1){for(;;){let o=i[0].address.sub(i[0].size*s.length).readPointer();if(o.isNull()||!o.readPointer().equals(a.readPointer()))break;s.unshift(new e.Thread(o))}break}}return s}return O(e.exports.threadGetAttachedThreads).map(a=>new e.Thread(a))}),w(e,"currentThread",()=>new e.Thread(e.exports.threadGetCurrent()).asNullable()),w(e,"mainThread",()=>e.attachedThreads[0])})(h||(h={}));var h;(function(e){let t=class extends v{static get Enum(){let n=(r,i=o=>o)=>i(e.corlib.class(r)).type.enumValue,s={VOID:n("System.Void"),BOOLEAN:n("System.Boolean"),CHAR:n("System.Char"),BYTE:n("System.SByte"),UBYTE:n("System.Byte"),SHORT:n("System.Int16"),USHORT:n("System.UInt16"),INT:n("System.Int32"),UINT:n("System.UInt32"),LONG:n("System.Int64"),ULONG:n("System.UInt64"),NINT:n("System.IntPtr"),NUINT:n("System.UIntPtr"),FLOAT:n("System.Single"),DOUBLE:n("System.Double"),POINTER:n("System.IntPtr",r=>r.field("m_value")),VALUE_TYPE:n("System.Decimal"),OBJECT:n("System.Object"),STRING:n("System.String"),CLASS:n("System.Array"),ARRAY:n("System.Void",r=>r.arrayClass),NARRAY:n("System.Void",r=>new e.Class(e.exports.classGetArrayClass(r,2))),GENERIC_INSTANCE:n("System.Int32",r=>r.interfaces.find(i=>i.name.endsWith("`1")))};return Reflect.defineProperty(this,"Enum",{value:s}),Y({...s,VAR:n("System.Action`1",r=>r.generics[0]),MVAR:n("System.Array",r=>r.method("AsReadOnly",1).generics[0])})}get class(){return new e.Class(e.exports.typeGetClass(this))}get fridaAlias(){function n(s){let r=s.class.fields.filter(i=>!i.isStatic);return r.length==0?["char"]:r.map(i=>i.type.fridaAlias)}if(this.isByReference)return"pointer";switch(this.enumValue){case e.Type.Enum.VOID:return"void";case e.Type.Enum.BOOLEAN:return"bool";case e.Type.Enum.CHAR:return"uchar";case e.Type.Enum.BYTE:return"int8";case e.Type.Enum.UBYTE:return"uint8";case e.Type.Enum.SHORT:return"int16";case e.Type.Enum.USHORT:return"uint16";case e.Type.Enum.INT:return"int32";case e.Type.Enum.UINT:return"uint32";case e.Type.Enum.LONG:return"int64";case e.Type.Enum.ULONG:return"uint64";case e.Type.Enum.FLOAT:return"float";case e.Type.Enum.DOUBLE:return"double";case e.Type.Enum.NINT:case e.Type.Enum.NUINT:case e.Type.Enum.POINTER:case e.Type.Enum.STRING:case e.Type.Enum.ARRAY:case e.Type.Enum.NARRAY:return"pointer";case e.Type.Enum.VALUE_TYPE:return this.class.isEnum?this.class.baseType.fridaAlias:n(this);case e.Type.Enum.CLASS:case e.Type.Enum.OBJECT:case e.Type.Enum.GENERIC_INSTANCE:return this.class.isStruct?n(this):this.class.isEnum?this.class.baseType.fridaAlias:"pointer";default:return"pointer"}}get isByReference(){return this.name.endsWith("&")}get isPrimitive(){switch(this.enumValue){case e.Type.Enum.BOOLEAN:case e.Type.Enum.CHAR:case e.Type.Enum.BYTE:case e.Type.Enum.UBYTE:case e.Type.Enum.SHORT:case e.Type.Enum.USHORT:case e.Type.Enum.INT:case e.Type.Enum.UINT:case e.Type.Enum.LONG:case e.Type.Enum.ULONG:case e.Type.Enum.FLOAT:case e.Type.Enum.DOUBLE:case e.Type.Enum.NINT:case e.Type.Enum.NUINT:return!0;default:return!1}}get name(){let n=e.exports.typeGetName(this);try{return n.readUtf8String()}finally{e.free(n)}}get object(){return new e.Object(e.exports.typeGetObject(this))}get enumValue(){return e.exports.typeGetTypeEnum(this)}is(n){return e.exports.typeEquals.isNull()?this.object.method("Equals").invoke(n.object):!!e.exports.typeEquals(this,n)}toString(){return this.name}};d([c],t.prototype,"class",null),d([c],t.prototype,"fridaAlias",null),d([c],t.prototype,"isByReference",null),d([c],t.prototype,"isPrimitive",null),d([c],t.prototype,"name",null),d([c],t.prototype,"object",null),d([c],t.prototype,"enumValue",null),d([c],t,"Enum",null),t=d([N],t),e.Type=t})(h||(h={}));var h;(function(e){class t extends v{type;constructor(n,s){super(n),this.type=s}box(){return new e.Object(e.exports.valueTypeBox(this.type.class,this))}field(n){return this.tryField(n)??p(`couldn't find non-static field ${n} in hierarchy of class ${this.type.name}`)}method(n,s=-1){return this.tryMethod(n,s)??p(`couldn't find non-static method ${n} in hierarchy of class ${this.type.name}`)}tryField(n){let s=this.type.class.tryField(n);if(s?.isStatic){for(let r of this.type.class.hierarchy())for(let i of r.fields)if(i.name==n&&!i.isStatic)return i.bind(this);return}return s?.bind(this)}tryMethod(n,s=-1){let r=this.type.class.tryMethod(n,s);if(r?.isStatic){for(let i of this.type.class.hierarchy())for(let o of i.methods)if(o.name==n&&!o.isStatic&&(s<0||o.parameterCount==s))return o.bind(this);return}return r?.bind(this)}toString(){let n=this.method("ToString",0);return this.isNull()?"null":n.class.isValueType?n.invoke().content??"null":this.box().toString()??"null"}}e.ValueType=t})(h||(h={}));globalThis.Il2Cpp=h;var F=class{assemblyImage=null;entityControllerClass=null;methods={IsSelected:null,CanUpgrade:null,GetLevel:null,GetMaxLevel:null,GetMaxUpgradeLevel:null,InstantUpgrade:null,Select:null,Unselect:null,GetUniqueId:null};isHooked=!1;stats={totalInstances:0,selectedInstances:0,upgradeableInstances:0,upgradesPerformed:0,startTime:Date.now()};constructor(){console.log("\u{1F680} Starting EntityController IL2CPP Hook (TypeScript)...")}async initialize(){try{return console.log("\u{1F50D} Initializing IL2CPP domain..."),this.assemblyImage=Il2Cpp.domain.assembly("Assembly-CSharp").image,this.assemblyImage?(console.log("\u2705 Assembly-CSharp found"),this.entityControllerClass=this.assemblyImage.class("EntityController"),this.entityControllerClass?(console.log("\u2705 EntityController class found"),console.log(`\u{1F4CB} Class info: ${this.entityControllerClass.name}`),this.setupMethods(),this.setupGlobalFunctions(),this.isHooked=!0,console.log("\u{1F3AF} EntityController hook setup complete!"),!0):(console.log("\u274C EntityController class not found"),this.listAvailableClasses(),!1)):(console.log("\u274C Failed to get Assembly-CSharp image"),this.listAvailableAssemblies(),!1)}catch(t){return console.log(`\u274C Initialization failed: ${t}`),!1}}setupMethods(){try{console.log("\u{1F527} Setting up method references..."),this.methods.IsSelected=this.entityControllerClass.method("IsSelected"),this.methods.CanUpgrade=this.entityControllerClass.method("CanUpgrade"),this.methods.GetLevel=this.entityControllerClass.method("GetLevel"),this.methods.GetMaxLevel=this.entityControllerClass.method("GetMaxLevel"),this.methods.GetMaxUpgradeLevel=this.entityControllerClass.method("GetMaxUpgradeLevel"),this.methods.InstantUpgrade=this.entityControllerClass.method("InstantUpgrade");try{this.methods.Select=this.entityControllerClass.method("Select"),this.methods.Unselect=this.entityControllerClass.method("Unselect"),this.methods.GetUniqueId=this.entityControllerClass.method("get_uniqueId")}catch(t){console.log(`\u26A0\uFE0F Some optional methods not found: ${t}`)}Object.entries(this.methods).forEach(([t,a])=>{console.log(a?`\u2705 Found method: ${t}`:`\u26A0\uFE0F Method not found: ${t}`)}),this.hookInstantUpgrade()}catch(t){console.log(`\u274C Method setup failed: ${t}`)}}hookInstantUpgrade(){if(!this.methods.InstantUpgrade){console.log("\u26A0\uFE0F InstantUpgrade method not available for hooking");return}try{let t=this.methods.InstantUpgrade.handle;Interceptor.attach(t,{onEnter:function(a){try{let n=a[0],s=new Il2Cpp.Object(n),r=s,i=r.get_uniqueId(),o=r.GetLevel();console.log(`\u26A1 InstantUpgrade called on entity ${i} (level ${o})`),this.entityId=i,this.levelBefore=o,this.entityObj=s}catch(n){console.log(`\u274C Error in InstantUpgrade onEnter: ${n}`)}},onLeave:function(a){try{if(this.entityObj&&this.entityId!==void 0){let s=this.entityObj.GetLevel();console.log(`\u2705 Entity ${this.entityId} upgraded: ${this.levelBefore} \u2192 ${s}`)}}catch(n){console.log(`\u274C Error in InstantUpgrade onLeave: ${n}`)}}}),console.log("\u2705 InstantUpgrade method hooked with Interceptor")}catch(t){console.log(`\u274C Failed to hook InstantUpgrade: ${t}`)}}getAllInstances(){try{if(!this.entityControllerClass)return console.log("\u274C EntityController class not initialized"),[];let t=Il2Cpp.gc.choose(this.entityControllerClass);return this.stats.totalInstances=t.length,console.log(`\u{1F50D} Found ${t.length} EntityController instances`),t}catch(t){return console.log(`\u274C Failed to get instances: ${t}`),[]}}autoUpgradeSelected(){try{console.log("\u{1F680} Starting auto-upgrade for selected entities...");let t=this.getAllInstances(),a=0,n=0,s=0,r=0;console.log(`\u{1F50D} Processing ${t.length} EntityController instances in batches...`);let i=50,o=Math.ceil(t.length/i),l=!1;for(let u=0;u<o&&!l;u++){let g=u*i,y=Math.min(g+i,t.length),m=t.slice(g,y);if(console.log(`\u{1F4E6} Processing batch ${u+1}/${o} (instances ${g}-${y-1})`),m.forEach((f,T)=>{let b=g+T;try{if(!this.isValidInstance(f)){r++,b<5&&console.log(`\u26A0\uFE0F Instance ${b} is invalid, skipping`);return}s++;let $=this.safeGetMethod(f,"IsSelected");if(!$){s<=5&&console.log(`\u26A0\uFE0F Instance ${b}: IsSelected method not accessible`);return}let _=this.safeInvokeMethod($,[],`Instance ${b} IsSelected`);if(_===null)return;if(_){n++,console.log(`\u{1F3AF} Processing selected entity ${b} (${n} selected so far)`);let E=this.safeUpgradeEntity(f,b);return E>0&&(a+=E),console.log(`\u2705 Selected entity upgrade complete. Skipping remaining ${t.length-b-1} instances.`),l=!0,a}else s<=10&&console.log(`\u{1F4CB} Instance ${b}: Not selected`)}catch($){let _=$ instanceof Error?$.message:String($),E=$ instanceof Error?$.stack:"No stack trace";console.log(`\u274C Error processing entity ${b}: ${_}`),console.log(`\u{1F4CB} Error details: ${E}`)}}),u<o-1){console.log(`\u23F3 Batch ${u+1} complete, waiting 500ms before next batch...`);let f=Date.now();for(;Date.now()-f<500;);}}return console.log("\u{1F4CA} Processing Summary:"),console.log(`   Total instances found: ${t.length}`),console.log(`   Valid instances: ${s}`),console.log(`   Invalid instances: ${r}`),console.log(`   Selected instances: ${n}`),console.log(`   Total upgrades performed: ${a}`),r>0&&console.log(`\u26A0\uFE0F Warning: ${r} invalid instances detected (likely destroyed objects)`),s>0&&n===0&&console.log("\u{1F4A1} Tip: No entities are currently selected. Select entities in-game first."),this.stats.selectedInstances=n,this.stats.upgradesPerformed+=a,console.log(`\u2705 Auto-upgrade complete! Selected: ${n}, Upgraded: ${a} levels`),a}catch(t){let a=t instanceof Error?t.message:String(t),n=t instanceof Error?t.stack:"No stack trace";return console.log(`\u274C Auto-upgrade failed: ${a}`),console.log(`\u{1F4CB} Error details: ${n}`),0}}isValidInstance(t){try{if(!t||!t.handle||t.handle.isNull())return!1;let a=t.class;if(!a||a.name!=="EntityController")return!1;try{let n=t.method("IsSelected");if(!n||!n.handle||n.handle.isNull())return!1;let s=t.method("GetLevel");if(!s||!s.handle||s.handle.isNull()||n.handle.readPointer().isNull())return!1}catch{return!1}return!0}catch{return!1}}safeGetMethod(t,a){try{if(!this.isValidInstance(t))return null;let n=t.method(a);return!n||!n.handle||n.handle.isNull()?null:n}catch{return null}}safeInvokeMethod(t,a=[],n="Unknown"){try{return t?t.invoke(...a):(console.log(`\u26A0\uFE0F ${n}: Method is null`),null)}catch(s){let r=s instanceof Error?s.message:String(s);return console.log(`\u274C ${n}: Method invocation failed - ${r}`),null}}safeUpgradeEntity(t,a){try{let n=0,s=this.safeGetMethod(t,"CanUpgrade"),r=this.safeGetMethod(t,"GetLevel"),i=this.safeGetMethod(t,"GetMaxUpgradeLevel"),o=this.safeGetMethod(t,"GetMaxLevel"),l=this.safeGetMethod(t,"InstantUpgrade");if(!s||!r||!i||!l)return console.log("\u26A0\uFE0F Instance 0: Required upgrade methods not accessible"),0;let u=this.safeInvokeMethod(r,[],"Instance 0 GetLevel (initial)"),g=this.safeInvokeMethod(i,[],"Instance 0 GetMaxUpgradeLevel"),y=o?this.safeInvokeMethod(o,[],"Instance 0 GetMaxLevel"):g;if(u===null||g===null)return console.log("\u274C Instance 0: Failed to get level information"),0;let m=Math.max(g||0,y||0),f=m-1;if(console.log(`\u{1F4CA} Instance 0: Starting upgrade from level ${u}/${f} (stopping at max-1 for manual final upgrade)`),console.log(`\u{1F4CB} Instance 0: GetMaxUpgradeLevel=${g}, GetMaxLevel=${y}, TrueMax=${m}, AutoUpgradeTo=${f}`),u>=f)return console.log(`\uFFFD Instance ${a}: Building at target level ${u}/${f} (ready for manual final upgrade to ${m})`),0;if(!this.safeInvokeMethod(s,[!0],`Instance ${a} CanUpgrade(true)`))return console.log(`\u{1F4CB} Instance ${a}: Cannot be upgraded (CanUpgrade returned false)`),0;let b=0;for(;u<f&&b<f;){if(u>=f){console.log(`\uFFFD Instance ${a}: Building at max level ${u}/${f}, stopping upgrades`);break}if(!this.safeInvokeMethod(s,[!0],`Instance ${a} CanUpgrade(true) (attempt ${b+1})`)){console.log(`\u26A0\uFE0F Instance ${a}: CanUpgrade returned false, stopping upgrades`);break}let _=u;console.log(`\u26A1 Instance 0: Calling InstantUpgrade (attempt ${b+1}) - Level ${_}/${f}`),this.safeInvokeMethod(l,[],`Instance 0 InstantUpgrade (attempt ${b+1})`),n++;let E=500,L=Date.now();for(;Date.now()-L<E;);let S=this.safeInvokeMethod(r,[],`Instance 0 GetLevel (after upgrade ${b+1})`);if(S===null){console.log("\u26A0\uFE0F Instance 0: Failed to get level after upgrade, stopping");break}if(u=S,S>_?console.log(`\u2705 Instance 0: Level increased! ${_} \u2192 ${S}`):console.log(S===_?`\u26A0\uFE0F Instance 0: Level unchanged (${_}), but InstantUpgrade was called`:`\u274C Instance 0: Level decreased (${_} \u2192 ${S}), unexpected!`),S>f){console.log(`\u26A0\uFE0F WARNING: Instance 0: Building over-leveled to ${S}, max is ${f}`);break}if(S>=f){console.log(`\u{1F3AF} Instance 0: Reached target level ${S}/${f} (final upgrade to ${m} left for manual)`);break}b++}return console.log(`\u{1F4CA} Instance 0: Auto-upgrade complete. Final level: ${u}/${f} (manual upgrade to ${m} available), InstantUpgrade calls: ${n}`),n}catch(n){return console.log(`\u274C Instance 0: Upgrade process failed - ${n}`),0}}getEntityInfo(t){try{if(!t)return console.log("\u274C No instance provided"),null;if(!this.isValidInstance(t))return console.log("\u274C Invalid instance provided"),null;let a=this.safeGetMethod(t,"IsSelected"),n=this.safeGetMethod(t,"CanUpgrade"),s=this.safeGetMethod(t,"GetLevel"),r=this.safeGetMethod(t,"GetMaxUpgradeLevel"),i=this.safeGetMethod(t,"get_uniqueId"),o={isValid:!0,isSelected:a?this.safeInvokeMethod(a,[],"GetEntityInfo IsSelected"):!1,canUpgrade:n?this.safeInvokeMethod(n,[!1],"GetEntityInfo CanUpgrade"):!1,currentLevel:s?this.safeInvokeMethod(s,[],"GetEntityInfo GetLevel"):0,maxLevel:r?this.safeInvokeMethod(r,[],"GetEntityInfo GetMaxUpgradeLevel"):0,uniqueId:i?this.safeInvokeMethod(i,[],"GetEntityInfo get_uniqueId"):"unknown"};return o.isSelected===null&&(o.isSelected=!1),o.canUpgrade===null&&(o.canUpgrade=!1),o.currentLevel===null&&(o.currentLevel=0),o.maxLevel===null&&(o.maxLevel=0),o.uniqueId===null&&(o.uniqueId="unknown"),console.log(`\u{1F4CB} Entity Info: ID=${o.uniqueId}, Selected=${o.isSelected}, Level=${o.currentLevel}/${o.maxLevel}, CanUpgrade=${o.canUpgrade}`),o}catch(a){let n=a instanceof Error?a.message:String(a);return console.log(`\u274C Failed to get entity info: ${n}`),{isValid:!1,error:n}}}setupGlobalFunctions(){console.log("\u{1F916} Setting up global automation functions...");try{globalThis.getAllEntityInstances=()=>this.getAllInstances(),globalThis.autoUpgradeSelected=()=>this.autoUpgradeSelected(),globalThis.getEntityInfo=t=>this.getEntityInfo(t),globalThis.getEntityStats=()=>this.getStats(),globalThis.forceUpgradeSelected=()=>this.forceUpgradeSelected(),globalThis.debugUpgradeFailure=t=>this.debugUpgradeFailure(t),globalThis.slowUpgradeSelected=()=>this.slowUpgradeSelected(),globalThis.simpleUpgradeSelected=()=>this.simpleUpgradeSelected(),console.log("\u2705 Global functions assigned to globalThis")}catch(t){console.log(`\u26A0\uFE0F globalThis assignment failed, trying alternative: ${t}`);try{global.getAllEntityInstances=()=>this.getAllInstances(),global.autoUpgradeSelected=()=>this.autoUpgradeSelected(),global.getEntityInfo=a=>this.getEntityInfo(a),global.getEntityStats=()=>this.getStats(),console.log("\u2705 Global functions assigned to global")}catch(a){console.log(`\u26A0\uFE0F global assignment also failed: ${a}`),console.log("\u{1F4A1} Functions will be available through entityHook instance")}}console.log("\u2705 Global functions setup complete!"),console.log("\u{1F4CB} Available functions:"),console.log("   - getAllEntityInstances()"),console.log("   - autoUpgradeSelected()"),console.log("   - forceUpgradeSelected() - bypasses CanUpgrade check"),console.log("   - slowUpgradeSelected() - slow upgrades with long delays"),console.log("   - simpleUpgradeSelected() - simple upgrades ignoring return values"),console.log("   - getEntityInfo(instance)"),console.log("   - getEntityStats()"),console.log("   - debugUpgradeFailure(instanceIndex) - detailed debugging")}getStats(){let t=Date.now()-this.stats.startTime;return console.log(`\u{1F4CA} Stats: Total: ${this.stats.totalInstances}, Selected: ${this.stats.selectedInstances}, Upgrades: ${this.stats.upgradesPerformed}, Runtime: ${t}ms`),{...this.stats}}forceUpgradeSelected(){try{console.log("\u26A1 Starting FORCE upgrade for selected entities (bypassing CanUpgrade)...");let t=this.getAllInstances(),a=0,n=0;return t.forEach((s,r)=>{try{if(!this.isValidInstance(s))return;let i=this.safeGetMethod(s,"IsSelected");if(!i)return;let o=this.safeInvokeMethod(i,[],`Instance ${r} IsSelected (force)`);if(o===null||!o)return;n++,console.log(`\u26A1 FORCE upgrading selected entity ${r}`);let l=this.safeGetMethod(s,"InstantUpgrade");if(l){let u=this.safeGetMethod(s,"GetLevel");if(u){let g=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (before force)`);console.log(`\u{1F50D} Instance ${r}: Level before force upgrade: ${g}`);let y=this.safeInvokeMethod(l,[],`Instance ${r} FORCE InstantUpgrade`),m=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (after force)`);console.log(`\u{1F50D} Instance ${r}: Level after force upgrade: ${m}`),m&&g&&m>g?(a++,console.log(`\u2705 Instance ${r}: FORCE upgrade successful! ${g} \u2192 ${m}`)):console.log(`\u274C Instance ${r}: FORCE upgrade failed - level unchanged`)}}}catch(i){let o=i instanceof Error?i.message:String(i);console.log(`\u274C Error force upgrading entity ${r}: ${o}`)}}),console.log(`\u2705 Force upgrade complete! Selected: ${n}, Upgraded: ${a} levels`),a}catch(t){let a=t instanceof Error?t.message:String(t);return console.log(`\u274C Force upgrade failed: ${a}`),0}}simpleUpgradeSelected(){try{console.log("\u26A1 Starting SIMPLE upgrade for selected entities (ignoring return values)...");let t=this.getAllInstances(),a=0,n=0;return t.forEach((s,r)=>{try{if(!this.isValidInstance(s))return;let i=this.safeGetMethod(s,"IsSelected");if(!i)return;let o=this.safeInvokeMethod(i,[],`Instance ${r} IsSelected (simple)`);if(o===null||!o)return;n++,console.log(`\u26A1 SIMPLE upgrading selected entity ${r}`);let l=this.safeGetMethod(s,"CanUpgrade"),u=this.safeGetMethod(s,"GetLevel"),g=this.safeGetMethod(s,"GetMaxUpgradeLevel"),y=this.safeGetMethod(s,"InstantUpgrade");if(!l||!u||!g||!y){console.log(`\u26A0\uFE0F Instance ${r}: Required methods not available`);return}let m=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (simple start)`),f=this.safeInvokeMethod(g,[],`Instance ${r} GetMaxUpgradeLevel (simple)`);if(m===null||f===null){console.log(`\u26A0\uFE0F Instance ${r}: Failed to get level information`);return}console.log(`\u{1F4CA} Instance ${r}: Starting simple upgrade from level ${m}/${f}`);let T=f-m;for(let b=0;b<T;b++){if(console.log(`\u26A1 Instance ${r}: Simple upgrade ${b+1}/${T}`),!this.safeInvokeMethod(l,[!0],`Instance ${r} CanUpgrade(true) (simple ${b+1})`)){console.log(`\u{1F4CB} Instance ${r}: Cannot upgrade further (simple check)`);break}let _=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (before simple ${b+1})`);this.safeInvokeMethod(y,[],`Instance ${r} SIMPLE InstantUpgrade ${b+1}`);let E=1e3,L=Date.now();for(;Date.now()-L<E;);let S=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (after simple ${b+1})`);if(S&&_&&S>_)a++,m=S,console.log(`\u2705 Instance ${r}: Simple upgrade successful! ${_} \u2192 ${S}`);else if(S===_)console.log(`\u26A0\uFE0F Instance ${r}: Level unchanged (${_}), may need more time or reached limit`);else{console.log(`\u274C Instance ${r}: Unexpected level change (${_} \u2192 ${S})`);break}if(m>=f){console.log(`\u{1F3AF} Instance ${r}: Reached max level ${m}/${f}`);break}}console.log(`\u26A1 Instance ${r}: Simple upgrade complete. Final level: ${m}/${f}`)}catch(i){let o=i instanceof Error?i.message:String(i);console.log(`\u274C Error simple upgrading entity ${r}: ${o}`)}}),console.log(`\u2705 Simple upgrade complete! Selected: ${n}, Upgraded: ${a} levels`),a}catch(t){let a=t instanceof Error?t.message:String(t);return console.log(`\u274C Simple upgrade failed: ${a}`),0}}slowUpgradeSelected(){try{console.log("\u{1F40C} Starting SLOW upgrade for selected entities (extended delays)...");let t=this.getAllInstances(),a=0,n=0;return t.forEach((s,r)=>{try{if(!this.isValidInstance(s))return;let i=this.safeGetMethod(s,"IsSelected");if(!i)return;let o=this.safeInvokeMethod(i,[],`Instance ${r} IsSelected (slow)`);if(o===null||!o)return;n++,console.log(`\u{1F40C} SLOW upgrading selected entity ${r}`);let l=this.safeGetMethod(s,"CanUpgrade"),u=this.safeGetMethod(s,"GetLevel"),g=this.safeGetMethod(s,"GetMaxUpgradeLevel"),y=this.safeGetMethod(s,"InstantUpgrade");if(!l||!u||!g||!y){console.log(`\u26A0\uFE0F Instance ${r}: Required methods not available`);return}if(!this.safeInvokeMethod(l,[!0],`Instance ${r} CanUpgrade(true) (slow)`)){console.log(`\u{1F4CB} Instance ${r}: Cannot be upgraded (slow check)`);return}let f=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (slow)`),T=this.safeInvokeMethod(g,[],`Instance ${r} GetMaxUpgradeLevel (slow)`);console.log(`\u{1F4CA} Instance ${r}: Starting slow upgrade from level ${f}/${T}`);let b=0,$=T-f;for(;f<T&&b<$;){console.log(`\u{1F40C} Instance ${r}: Slow upgrade attempt ${b+1}/${$}`);let _=5e3;console.log(`\u23F3 Waiting ${_}ms to avoid anti-debug detection...`);let E=Date.now();for(;Date.now()-E<_;);if(!this.safeInvokeMethod(l,[!0],`Instance ${r} CanUpgrade(true) (slow attempt ${b+1})`)){console.log(`\u26A0\uFE0F Instance ${r}: Can no longer upgrade, stopping`);break}let S=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (before slow upgrade ${b+1})`),j=!1;for(let G=0;G<5;G++){if(console.log(`\u{1F40C} Instance ${r}: InstantUpgrade attempt ${G+1}/5`),this.safeInvokeMethod(y,[],`Instance ${r} SLOW InstantUpgrade (retry ${G+1})`)!==null){j=!0;break}if(G<4){console.log("\u23F3 Retry failed, waiting 3000ms before next retry...");let V=Date.now();for(;Date.now()-V<3e3;);}}if(!j){console.log(`\u274C Instance ${r}: All slow upgrade retries failed, stopping`);break}let x=this.safeInvokeMethod(u,[],`Instance ${r} GetLevel (after slow upgrade ${b+1})`);if(x&&S&&x>S)a++,f=x,console.log(`\u2705 Instance ${r}: Slow upgrade successful! ${S} \u2192 ${x}`);else{console.log(`\u26A0\uFE0F Instance ${r}: Level didn't change (${S} \u2192 ${x}), stopping`);break}b++}console.log(`\u{1F40C} Instance ${r}: Slow upgrade complete. Final level: ${f}/${T}`)}catch(i){let o=i instanceof Error?i.message:String(i);console.log(`\u274C Error slow upgrading entity ${r}: ${o}`)}}),console.log(`\u2705 Slow upgrade complete! Selected: ${n}, Upgraded: ${a} levels`),a}catch(t){let a=t instanceof Error?t.message:String(t);return console.log(`\u274C Slow upgrade failed: ${a}`),0}}debugUpgradeFailure(t){try{let a=this.getAllInstances();if(t>=a.length){console.log(`\u274C Instance ${t} not found (max: ${a.length-1})`);return}let n=a[t];if(console.log(`\u{1F50D} Debugging upgrade failure for instance ${t}...`),!this.isValidInstance(n)){console.log(`\u274C Instance ${t} is invalid`);return}console.log(`\u{1F4CB} Available methods on instance ${t}:`);try{n.class.methods.slice(0,20).forEach((o,l)=>{console.log(`   ${l}: ${o.name}`)})}catch(r){console.log(`   Could not enumerate methods: ${r}`)}["IsSelected","CanUpgrade","GetLevel","GetMaxLevel","GetMaxUpgradeLevel","InstantUpgrade","GetUpgradeCost","GetUpgradeTime","IsUpgrading","IsBusy","GetState","CanCollect","IsCollecting","GetResourceType"].forEach(r=>{let i=this.safeGetMethod(n,r);if(i){if(console.log(`\u2705 Method ${r} is available`),["IsSelected","GetLevel","GetMaxLevel","GetMaxUpgradeLevel","IsUpgrading","IsBusy","GetState"].includes(r)){let o=this.safeInvokeMethod(i,[],`Debug ${r}`);console.log(`   ${r}() = ${o}`)}}else console.log(`\u274C Method ${r} not found`)})}catch(a){let n=a instanceof Error?a.message:String(a);console.log(`\u274C Debug failed: ${n}`)}}listAvailableAssemblies(){try{console.log("\u{1F4A1} Available assemblies:"),Il2Cpp.domain.assemblies.slice(0,10).forEach((a,n)=>{console.log(`   ${n}: ${a.name}`)})}catch{console.log("   Could not enumerate assemblies")}}listAvailableClasses(){if(this.assemblyImage)try{console.log("\u{1F4A1} Available classes (first 10):");let t=this.assemblyImage.classes;for(let n=0;n<Math.min(10,t.length);n++)console.log(`   ${n}: ${t[n].name}`);let a=t.filter(n=>n.name.toLowerCase().includes("entity")||n.name.toLowerCase().includes("controller"));a.length>0&&(console.log("\u{1F50D} Found entity/controller related classes:"),a.forEach((n,s)=>{console.log(`   ${s}: ${n.name}`)}))}catch{console.log("   Could not enumerate classes")}}};Il2Cpp.perform(async()=>{console.log("\u{1F680} EntityController Hook - Il2Cpp bridge context established");try{await new Promise(a=>setTimeout(a,5e3));let e=new F;if(await e.initialize()){console.log("\u2705 EntityController hook ready!");try{globalThis.entityHook=e,console.log("\u2705 Hook instance available as 'entityHook'")}catch(a){console.log(`\u26A0\uFE0F Could not assign to globalThis: ${a}`);try{global.entityHook=e,console.log("\u2705 Hook instance available as 'entityHook' (via global)")}catch(n){console.log(`\u26A0\uFE0F Could not assign to global either: ${n}`)}}setTimeout(()=>{console.log("\u{1F9EA} Testing global function accessibility...");try{typeof globalThis.getAllEntityInstances=="function"?console.log("\u2705 getAllEntityInstances is accessible globally"):console.log("\u274C getAllEntityInstances is not accessible globally")}catch(a){console.log(`\u26A0\uFE0F Global function test failed: ${a}`)}},1e3),console.log("\u{1F4A1} Use autoUpgradeSelected() to upgrade selected entities"),console.log("\u{1F4A1} Use entityHook.getAllInstances() if global functions don't work")}else console.log("\u274C Failed to initialize EntityController hook")}catch(e){console.log(`\u274C Fatal error: ${e}`)}});
