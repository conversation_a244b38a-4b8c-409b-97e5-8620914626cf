{"name": "frida-domnations", "version": "1.0.0", "description": "A Frida script for enhancing Pokemon GO gameplay using IL2CPP bridge", "main": "src/index.ts", "scripts": {"build": "frida-compile src/index.ts -o dist/agent.js -c", "build-enhanced": "frida-compile src/enhanced-goody-hut.ts -o dist/enhanced-agent.js -c", "build-test": "frida-compile src/test-minimal.ts -o dist/test-agent.js -c", "build-v2": "frida-compile src/enhanced-goody-hut-v2.ts -o dist/enhanced-v2-agent.js -c", "build-connection": "frida-compile src/connection-test.ts -o dist/connection-test.js -c", "build-robust": "frida-compile src/robust-instance-handler.ts -o dist/robust-handler.js -c", "build-entitycontroller": "frida-compile src/entitycontroller-hook.ts -o dist/entitycontroller-hook.js -c", "build-auto-discovery": "frida-compile src/auto-discovery-goody-hut.ts -o dist/auto-discovery-goody-hut.js -c", "watch": "frida-compile src/index.ts -o dist/agent.js -w", "watch-enhanced": "frida-compile src/enhanced-goody-hut.ts -o dist/enhanced-agent.js -w", "spawn": "frida -U -f com.nexonm.dominations.adk -l dist/agent.js", "spawn-enhanced": "frida -U -f com.nexonm.dominations.adk -l dist/enhanced-agent.js", "spawn-test": "frida -U -f com.nexonm.dominations.adk -l dist/test-agent.js", "spawn-v2": "frida -U -f com.nexonm.dominations.adk -l dist/enhanced-v2-agent.js", "test-connection": "frida -U -f com.nexonm.dominations.adk -l dist/connection-test.js", "attach": "frida -U -n com.nexonm.dominations.adk -l dist/agent.js", "attach-enhanced": "frida -U -n com.nexonm.dominations.adk -l dist/enhanced-agent.js", "trace-all": "frida-trace -U -f com.nexonm.dominations.adk -i '*'", "trace-goody": "frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*'", "trace-collect": "frida-trace -U -f com.nexonm.dominations.adk -i '*Collect*'", "trace-il2cpp": "frida-trace -U -f com.nexonm.dominations.adk -i 'libil2cpp.so!*'", "trace-unity": "frida-trace -U -f com.nexonm.dominations.adk -i 'libunity.so!*'", "trace-attach": "frida-trace -U com.nexonm.dominations.adk -i '*GoodyHut*'", "trace-safe": "frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*' -S trace-handler-safe.js", "trace-basic": "frida-trace -U -f com.nexonm.dominations.adk -i '*GoodyHut*'", "hook-addresses": "frida -U -f com.nexonm.dominations.adk -l address-hook.js", "spawn-auto-discovery": "frida -U -f com.nexonm.dominations.adk -l dist/auto-discovery-goody-hut.js"}, "dependencies": {"@types/frida-gum": "^19.0.1", "@types/node": "^24.3.0", "frida-compile": "^19.0.4", "frida-il2cpp-bridge": "^0.12.0", "fs": "^0.0.1-security", "typescript": "^5.9.2"}}